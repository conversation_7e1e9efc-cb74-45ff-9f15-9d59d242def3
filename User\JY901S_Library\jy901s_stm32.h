/**
 * @file jy901s_stm32.h
 * @brief JY901S陀螺仪STM32平台移植适配层
 * @version 1.0
 * @date 2024-07-28
 * <AUTHOR> Agent
 * 
 * @note 这个文件提供了JY901S库在STM32平台上的移植适配
 *       只需要包含这个文件即可在STM32项目中使用JY901S库
 */

#ifndef __JY901S_STM32_H__
#define __JY901S_STM32_H__

#include "jy901s.h"

// STM32 HAL库头文件 - 根据您的项目修改
#include "main.h"           // 包含HAL库和GPIO定义
#include "i2c.h"            // I2C句柄定义

// 如果您的项目结构不同，请修改上面的包含路径

/**
 * @brief STM32平台JY901S配置结构体
 */
typedef struct {
    I2C_HandleTypeDef *hi2c;        // I2C句柄指针
    uint8_t device_addr;            // JY901S设备地址
    uint16_t timeout_ms;            // I2C超时时间
    bool auto_calibration;          // 是否自动校准
} JY901S_STM32_Config_t;

/**
 * @brief STM32平台JY901S句柄
 */
typedef struct {
    JY901S_Handle_t jy901s;         // JY901S通用句柄
    I2C_HandleTypeDef *hi2c;        // STM32 I2C句柄
} JY901S_STM32_Handle_t;

// --- STM32平台移植接口函数 ---

/**
 * @brief STM32平台I2C写函数
 * @param device_addr I2C设备地址 (7位地址)
 * @param reg_addr 寄存器地址
 * @param data 要写入的数据指针
 * @param len 数据长度
 * @param timeout_ms 超时时间 (毫秒)
 * @return true-成功, false-失败
 */
bool JY901S_STM32_I2C_Write(uint8_t device_addr, uint8_t reg_addr, 
                            uint8_t *data, uint16_t len, uint16_t timeout_ms);

/**
 * @brief STM32平台I2C读函数
 * @param device_addr I2C设备地址 (7位地址)
 * @param reg_addr 寄存器地址
 * @param data 读取数据缓冲区指针
 * @param len 数据长度
 * @param timeout_ms 超时时间 (毫秒)
 * @return true-成功, false-失败
 */
bool JY901S_STM32_I2C_Read(uint8_t device_addr, uint8_t reg_addr, 
                           uint8_t *data, uint16_t len, uint16_t timeout_ms);

/**
 * @brief STM32平台延时函数
 * @param ms 延时毫秒数
 */
void JY901S_STM32_Delay(uint32_t ms);

/**
 * @brief STM32平台获取系统时间戳函数
 * @return 系统时间戳 (毫秒)
 */
uint32_t JY901S_STM32_GetTick(void);

// --- STM32平台高级封装函数 ---

/**
 * @brief 初始化STM32平台的JY901S
 * @param handle STM32句柄指针
 * @param config STM32配置参数指针
 * @return true-成功, false-失败
 */
bool JY901S_STM32_Init(JY901S_STM32_Handle_t *handle, JY901S_STM32_Config_t *config);

/**
 * @brief 读取所有传感器数据 (STM32封装)
 * @param handle STM32句柄指针
 * @return true-成功, false-失败
 */
bool JY901S_STM32_ReadAllData(JY901S_STM32_Handle_t *handle);

/**
 * @brief 获取Roll角度 (STM32封装)
 * @param handle STM32句柄指针
 * @return Roll角度 (°)
 */
float JY901S_STM32_GetRoll(JY901S_STM32_Handle_t *handle);

/**
 * @brief 获取Pitch角度 (STM32封装)
 * @param handle STM32句柄指针
 * @return Pitch角度 (°)
 */
float JY901S_STM32_GetPitch(JY901S_STM32_Handle_t *handle);

/**
 * @brief 获取Yaw角度 (STM32封装)
 * @param handle STM32句柄指针
 * @return Yaw角度 (°)
 */
float JY901S_STM32_GetYaw(JY901S_STM32_Handle_t *handle);

/**
 * @brief 获取加速度数据 (STM32封装)
 * @param handle STM32句柄指针
 * @param acc_x X轴加速度指针 (g)
 * @param acc_y Y轴加速度指针 (g)
 * @param acc_z Z轴加速度指针 (g)
 * @return true-成功, false-失败
 */
bool JY901S_STM32_GetAcceleration(JY901S_STM32_Handle_t *handle, 
                                  float *acc_x, float *acc_y, float *acc_z);

/**
 * @brief 获取角速度数据 (STM32封装)
 * @param handle STM32句柄指针
 * @param gyro_x X轴角速度指针 (°/s)
 * @param gyro_y Y轴角速度指针 (°/s)
 * @param gyro_z Z轴角速度指针 (°/s)
 * @return true-成功, false-失败
 */
bool JY901S_STM32_GetGyroscope(JY901S_STM32_Handle_t *handle, 
                               float *gyro_x, float *gyro_y, float *gyro_z);

/**
 * @brief 获取温度数据 (STM32封装)
 * @param handle STM32句柄指针
 * @return 温度 (°C)
 */
float JY901S_STM32_GetTemperature(JY901S_STM32_Handle_t *handle);

/**
 * @brief 校准传感器 (STM32封装)
 * @param handle STM32句柄指针
 * @return true-成功, false-失败
 */
bool JY901S_STM32_Calibrate(JY901S_STM32_Handle_t *handle);

/**
 * @brief 复位传感器 (STM32封装)
 * @param handle STM32句柄指针
 * @return true-成功, false-失败
 */
bool JY901S_STM32_Reset(JY901S_STM32_Handle_t *handle);

/**
 * @brief 检查数据是否就绪 (STM32封装)
 * @param handle STM32句柄指针
 * @return true-数据就绪, false-数据未就绪
 */
bool JY901S_STM32_IsDataReady(JY901S_STM32_Handle_t *handle);

/**
 * @brief 获取错误计数 (STM32封装)
 * @param handle STM32句柄指针
 * @return 错误计数
 */
uint32_t JY901S_STM32_GetErrorCount(JY901S_STM32_Handle_t *handle);

// --- 便捷宏定义 ---

/**
 * @brief 创建默认STM32配置
 * @param _hi2c I2C句柄
 * @param _addr 设备地址 (默认JY901S_I2C_ADDR)
 * @param _timeout 超时时间 (默认1000ms)
 * @param _auto_cal 是否自动校准 (默认false)
 */
#define JY901S_STM32_DEFAULT_CONFIG(_hi2c, _addr, _timeout, _auto_cal) \
    { \
        .hi2c = (_hi2c), \
        .device_addr = (_addr), \
        .timeout_ms = (_timeout), \
        .auto_calibration = (_auto_cal) \
    }

/**
 * @brief 创建标准JY901S配置 (使用默认参数)
 * @param _hi2c I2C句柄
 */
#define JY901S_STM32_STANDARD_CONFIG(_hi2c) \
    JY901S_STM32_DEFAULT_CONFIG((_hi2c), JY901S_I2C_ADDR, 1000, false)

// --- 全局变量声明 (用于移植接口) ---
extern I2C_HandleTypeDef *g_jy901s_hi2c;

#endif /* __JY901S_STM32_H__ */
