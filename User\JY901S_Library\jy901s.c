/**
 * @file jy901s.c
 * @brief JY901S陀螺仪I2C驱动库实现 - 便于移植版本
 * @version 1.0
 * @date 2024-07-28
 * <AUTHOR> Agent
 */

#include "jy901s.h"

// 内部辅助函数声明
static bool JY901S_WriteRegister(JY901S_Handle_t *handle, uint8_t reg, uint8_t value);
static bool JY901S_ReadRegisters(JY901S_Handle_t *handle, uint8_t reg, uint8_t *data, uint16_t len);
static void JY901S_ConvertRawData(JY901S_Handle_t *handle);
static bool JY901S_ValidateHandle(JY901S_Handle_t *handle);

/**
 * @brief 初始化JY901S驱动
 */
bool JY901S_Init(JY901S_Handle_t *handle, 
                 JY901S_Config_t *config,
                 JY901S_I2C_Write_t i2c_write,
                 JY901S_I2C_Read_t i2c_read,
                 JY901S_Delay_t delay,
                 JY901S_GetTick_t get_tick)
{
    // 参数检查
    if (!handle || !config || !i2c_write || !i2c_read || !delay || !get_tick) {
        return false;
    }
    
    // 保存配置和接口函数
    handle->config = *config;
    handle->i2c_write = i2c_write;
    handle->i2c_read = i2c_read;
    handle->delay = delay;
    handle->get_tick = get_tick;
    
    // 初始化数据结构
    for (int i = 0; i < 3; i++) {
        handle->data.acc_raw[i] = 0;
        handle->data.gyro_raw[i] = 0;
        handle->data.angle_raw[i] = 0;
        handle->data.mag_raw[i] = 0;
        handle->data.acc[i] = 0.0f;
        handle->data.gyro[i] = 0.0f;
        handle->data.angle[i] = 0.0f;
        handle->data.mag[i] = 0.0f;
    }
    handle->data.temp_raw = 0;
    handle->data.temperature = 0.0f;
    handle->data.data_ready = false;
    handle->data.timestamp = 0;
    handle->data.error_count = 0;
    
    handle->initialized = false;
    handle->last_read_time = 0;
    
    // 等待传感器启动
    handle->delay(100);
    
    // 尝试读取一次数据验证通信
    uint8_t test_data[6];
    if (!JY901S_ReadRegisters(handle, JY901S_REG_ACC, test_data, 6)) {
        handle->data.error_count++;
        return false;
    }
    
    // 如果启用自动校准，执行校准
    if (handle->config.auto_calibration) {
        if (!JY901S_Calibrate(handle)) {
            handle->data.error_count++;
            // 校准失败不影响初始化，继续执行
        }
    }
    
    handle->initialized = true;
    return true;
}

/**
 * @brief 读取所有传感器数据
 */
bool JY901S_ReadAllData(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return false;
    }
    
    bool success = true;
    
    // 读取各种传感器数据
    success &= JY901S_ReadAcceleration(handle);
    success &= JY901S_ReadGyroscope(handle);
    success &= JY901S_ReadAngle(handle);
    success &= JY901S_ReadTemperature(handle);
    
    if (success) {
        // 转换原始数据为物理量
        JY901S_ConvertRawData(handle);
        handle->data.data_ready = true;
        handle->data.timestamp = handle->get_tick();
        handle->last_read_time = handle->data.timestamp;
    } else {
        handle->data.error_count++;
        handle->data.data_ready = false;
    }
    
    return success;
}

/**
 * @brief 读取加速度数据
 */
bool JY901S_ReadAcceleration(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return false;
    }
    
    uint8_t data[6];
    if (!JY901S_ReadRegisters(handle, JY901S_REG_ACC, data, 6)) {
        handle->data.error_count++;
        return false;
    }
    
    // 组合16位数据 (小端序)
    handle->data.acc_raw[0] = (int16_t)((data[1] << 8) | data[0]);  // X轴
    handle->data.acc_raw[1] = (int16_t)((data[3] << 8) | data[2]);  // Y轴
    handle->data.acc_raw[2] = (int16_t)((data[5] << 8) | data[4]);  // Z轴
    
    // 转换为物理量
    for (int i = 0; i < 3; i++) {
        handle->data.acc[i] = handle->data.acc_raw[i] * JY901S_ACC_SCALE;
    }
    
    return true;
}

/**
 * @brief 读取角速度数据
 */
bool JY901S_ReadGyroscope(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return false;
    }
    
    uint8_t data[6];
    if (!JY901S_ReadRegisters(handle, JY901S_REG_GYRO, data, 6)) {
        handle->data.error_count++;
        return false;
    }
    
    // 组合16位数据 (小端序)
    handle->data.gyro_raw[0] = (int16_t)((data[1] << 8) | data[0]);  // X轴
    handle->data.gyro_raw[1] = (int16_t)((data[3] << 8) | data[2]);  // Y轴
    handle->data.gyro_raw[2] = (int16_t)((data[5] << 8) | data[4]);  // Z轴
    
    // 转换为物理量
    for (int i = 0; i < 3; i++) {
        handle->data.gyro[i] = handle->data.gyro_raw[i] * JY901S_GYRO_SCALE;
    }
    
    return true;
}

/**
 * @brief 读取角度数据
 */
bool JY901S_ReadAngle(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return false;
    }
    
    uint8_t data[6];
    if (!JY901S_ReadRegisters(handle, JY901S_REG_ANGLE, data, 6)) {
        handle->data.error_count++;
        return false;
    }
    
    // 组合16位数据 (小端序)
    handle->data.angle_raw[0] = (int16_t)((data[1] << 8) | data[0]);  // Roll
    handle->data.angle_raw[1] = (int16_t)((data[3] << 8) | data[2]);  // Pitch
    handle->data.angle_raw[2] = (int16_t)((data[5] << 8) | data[4]);  // Yaw
    
    // 转换为物理量
    for (int i = 0; i < 3; i++) {
        handle->data.angle[i] = handle->data.angle_raw[i] * JY901S_ANGLE_SCALE;
    }
    
    return true;
}

/**
 * @brief 读取温度数据
 */
bool JY901S_ReadTemperature(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return false;
    }
    
    uint8_t data[2];
    if (!JY901S_ReadRegisters(handle, JY901S_REG_TEMP, data, 2)) {
        handle->data.error_count++;
        return false;
    }
    
    // 组合16位数据 (小端序)
    handle->data.temp_raw = (int16_t)((data[1] << 8) | data[0]);
    
    // 转换为物理量
    handle->data.temperature = handle->data.temp_raw * JY901S_TEMP_SCALE;
    
    return true;
}

/**
 * @brief 校准传感器
 */
bool JY901S_Calibrate(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return false;
    }
    
    // 发送校准命令
    if (!JY901S_WriteRegister(handle, JY901S_REG_CALSW, 0x01)) {
        handle->data.error_count++;
        return false;
    }
    
    // 等待校准完成 (约3秒)
    handle->delay(3000);
    
    return true;
}

// --- 数据获取函数实现 ---

/**
 * @brief 获取Roll角度
 */
float JY901S_GetRoll(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return 0.0f;
    }
    return handle->data.angle[0];
}

/**
 * @brief 获取Pitch角度
 */
float JY901S_GetPitch(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return 0.0f;
    }
    return handle->data.angle[1];
}

/**
 * @brief 获取Yaw角度
 */
float JY901S_GetYaw(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return 0.0f;
    }
    return handle->data.angle[2];
}

/**
 * @brief 获取X轴加速度
 */
float JY901S_GetAccX(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return 0.0f;
    }
    return handle->data.acc[0];
}

/**
 * @brief 获取Y轴加速度
 */
float JY901S_GetAccY(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return 0.0f;
    }
    return handle->data.acc[1];
}

/**
 * @brief 获取Z轴加速度
 */
float JY901S_GetAccZ(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return 0.0f;
    }
    return handle->data.acc[2];
}

/**
 * @brief 获取X轴角速度
 */
float JY901S_GetGyroX(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return 0.0f;
    }
    return handle->data.gyro[0];
}

/**
 * @brief 获取Y轴角速度
 */
float JY901S_GetGyroY(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return 0.0f;
    }
    return handle->data.gyro[1];
}

/**
 * @brief 获取Z轴角速度
 */
float JY901S_GetGyroZ(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return 0.0f;
    }
    return handle->data.gyro[2];
}

/**
 * @brief 获取温度
 */
float JY901S_GetTemperature(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return 0.0f;
    }
    return handle->data.temperature;
}

/**
 * @brief 检查数据是否就绪
 */
bool JY901S_IsDataReady(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return false;
    }
    return handle->data.data_ready;
}

/**
 * @brief 获取错误计数
 */
uint32_t JY901S_GetErrorCount(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return 0;
    }
    return handle->data.error_count;
}

// --- 内部辅助函数实现 ---

/**
 * @brief 写寄存器
 */
static bool JY901S_WriteRegister(JY901S_Handle_t *handle, uint8_t reg, uint8_t value)
{
    return handle->i2c_write(handle->config.device_addr, reg, &value, 1, handle->config.timeout_ms);
}

/**
 * @brief 读寄存器
 */
static bool JY901S_ReadRegisters(JY901S_Handle_t *handle, uint8_t reg, uint8_t *data, uint16_t len)
{
    return handle->i2c_read(handle->config.device_addr, reg, data, len, handle->config.timeout_ms);
}

/**
 * @brief 转换原始数据为物理量
 */
static void JY901S_ConvertRawData(JY901S_Handle_t *handle)
{
    // 加速度转换
    for (int i = 0; i < 3; i++) {
        handle->data.acc[i] = handle->data.acc_raw[i] * JY901S_ACC_SCALE;
    }

    // 角速度转换
    for (int i = 0; i < 3; i++) {
        handle->data.gyro[i] = handle->data.gyro_raw[i] * JY901S_GYRO_SCALE;
    }

    // 角度转换
    for (int i = 0; i < 3; i++) {
        handle->data.angle[i] = handle->data.angle_raw[i] * JY901S_ANGLE_SCALE;
    }

    // 温度转换
    handle->data.temperature = handle->data.temp_raw * JY901S_TEMP_SCALE;
}

/**
 * @brief 验证句柄有效性
 */
static bool JY901S_ValidateHandle(JY901S_Handle_t *handle)
{
    return (handle != NULL && handle->initialized);
}

/**
 * @brief 复位传感器
 */
bool JY901S_Reset(JY901S_Handle_t *handle)
{
    if (!JY901S_ValidateHandle(handle)) {
        return false;
    }
    
    // 发送复位命令
    if (!JY901S_WriteRegister(handle, JY901S_REG_CALSW, 0x00)) {
        handle->data.error_count++;
        return false;
    }
    
    // 等待复位完成
    handle->delay(100);
    
    return true;
}
