#include "mg513x_app.h"

// MG513X电机实例
MG513X_Motor left_mg513x;
MG513X_Motor right_mg513x;

/**
 * @brief 初始化MG513X电机应用
 */
void MG513X_App_Init(void)
{
    // 初始化左电机
    // 注意：这里的引脚配置需要根据您的实际硬件连接进行修改
    MG513X_Init(&left_mg513x,
                &htim1, TIM_CHANNEL_1,           // PWM定时器和通道
                AIN1_GPIO_Port, AIN1_Pin,        // 方向控制引脚1
                AIN2_GPIO_Port, AIN2_Pin,        // 方向控制引脚2
                &htim3,                          // 编码器定时器
                0,                               // 电机方向不反转
                0,                               // 编码器方向不反转
                MG513X_DEFAULT_DEAD_BAND);       // 死区补偿
    
    // 初始化右电机
    MG513X_Init(&right_mg513x,
                &htim1, TIM_CHANNEL_2,           // PWM定时器和通道
                BIN1_GPIO_Port, BIN1_Pin,        // 方向控制引脚1
                BIN2_GPIO_Port, BIN2_Pin,        // 方向控制引脚2
                &htim4,                          // 编码器定时器
                0,                               // 电机方向不反转
                1,                               // 编码器方向反转(通常右轮需要反转)
                MG513X_DEFAULT_DEAD_BAND);       // 死区补偿
    
    // 打印初始化信息
    Uart_Printf(&huart1, "=== MG513X Motors Initialized ===\r\n");
    Uart_Printf(&huart1, "Encoder PPR: %d\r\n", MG513X_ENCODER_PPR);
    Uart_Printf(&huart1, "Wheel Diameter: %.1f cm\r\n", MG513X_WHEEL_DIAMETER_CM);
    Uart_Printf(&huart1, "Sampling Time: %.3f s\r\n", MG513X_SAMPLING_TIME_S);
}

/**
 * @brief MG513X电机应用任务 (需要周期性调用)
 */
void MG513X_App_Task(void)
{
    // 更新编码器数据
    MG513X_UpdateEncoder(&left_mg513x);
    MG513X_UpdateEncoder(&right_mg513x);
    
    // 可选：打印调试信息 (取消注释以启用)
    /*
    static uint16_t debug_counter = 0;
    if (++debug_counter >= 200) { // 每1秒打印一次 (5ms * 200 = 1s)
        debug_counter = 0;
        Uart_Printf(&huart1, "L:%.1fcm/s R:%.1fcm/s L_cnt:%ld R_cnt:%ld\r\n",
                   left_mg513x.speed_cm_s, right_mg513x.speed_cm_s,
                   left_mg513x.encoder_total_count, right_mg513x.encoder_total_count);
    }
    */
}

/**
 * @brief 设置左电机速度
 */
void MG513X_App_SetLeftSpeed(int speed)
{
    MG513X_SetSpeed(&left_mg513x, speed);
}

/**
 * @brief 设置右电机速度
 */
void MG513X_App_SetRightSpeed(int speed)
{
    MG513X_SetSpeed(&right_mg513x, speed);
}

/**
 * @brief 设置双电机速度
 */
void MG513X_App_SetSpeed(int left_speed, int right_speed)
{
    MG513X_SetSpeed(&left_mg513x, left_speed);
    MG513X_SetSpeed(&right_mg513x, right_speed);
}

/**
 * @brief 获取左电机速度 (cm/s)
 */
float MG513X_App_GetLeftSpeedCmS(void)
{
    return MG513X_GetSpeedCmS(&left_mg513x);
}

/**
 * @brief 获取右电机速度 (cm/s)
 */
float MG513X_App_GetRightSpeedCmS(void)
{
    return MG513X_GetSpeedCmS(&right_mg513x);
}

/**
 * @brief 停止所有电机
 */
void MG513X_App_StopAll(void)
{
    MG513X_Stop(&left_mg513x);
    MG513X_Stop(&right_mg513x);
}

/**
 * @brief 刹车所有电机
 */
void MG513X_App_BrakeAll(void)
{
    MG513X_Brake(&left_mg513x);
    MG513X_Brake(&right_mg513x);
}

/**
 * @brief 重置所有编码器
 */
void MG513X_App_ResetEncoders(void)
{
    MG513X_ResetEncoder(&left_mg513x);
    MG513X_ResetEncoder(&right_mg513x);
}

/**
 * @brief 获取左电机编码器总计数
 */
int32_t MG513X_App_GetLeftTotalCount(void)
{
    return MG513X_GetTotalCount(&left_mg513x);
}

/**
 * @brief 获取右电机编码器总计数
 */
int32_t MG513X_App_GetRightTotalCount(void)
{
    return MG513X_GetTotalCount(&right_mg513x);
}
