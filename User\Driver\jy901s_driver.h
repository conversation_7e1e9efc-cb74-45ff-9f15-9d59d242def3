#ifndef __JY901S_DRIVER_H__
#define __JY901S_DRIVER_H__

#include <stdint.h>
#include <stdbool.h>

// --- JY901S I2C地址和寄存器定义 ---
#define JY901S_I2C_ADDR         0x50    // JY901S默认I2C地址 (7位地址)
#define JY901S_I2C_ADDR_WRITE   (JY901S_I2C_ADDR << 1)       // 写地址
#define JY901S_I2C_ADDR_READ    ((JY901S_I2C_ADDR << 1) | 1) // 读地址

// --- 寄存器地址定义 ---
#define JY901S_REG_TIME         0x30    // 时间寄存器
#define JY901S_REG_ACC          0x34    // 加速度寄存器
#define JY901S_REG_GYRO         0x37    // 角速度寄存器  
#define JY901S_REG_ANGLE        0x3D    // 角度寄存器
#define JY901S_REG_MAG          0x3A    // 磁场寄存器
#define JY901S_REG_PRESS        0x40    // 气压寄存器
#define JY901S_REG_DPORT        0x43    // 端口寄存器
#define JY901S_REG_TEMP         0x46    // 温度寄存器

// --- 控制寄存器 ---
#define JY901S_REG_SAVE         0x00    // 保存设置
#define JY901S_REG_CALSW        0x01    // 校准
#define JY901S_REG_RSW          0x02    // 回传速率
#define JY901S_REG_RRATE        0x03    // 回传内容
#define JY901S_REG_BAUD         0x04    // 波特率
#define JY901S_REG_AXOFFSET     0x05    // X轴加速度偏移
#define JY901S_REG_AYOFFSET     0x06    // Y轴加速度偏移
#define JY901S_REG_AZOFFSET     0x07    // Z轴加速度偏移
#define JY901S_REG_GXOFFSET     0x08    // X轴角速度偏移
#define JY901S_REG_GYOFFSET     0x09    // Y轴角速度偏移
#define JY901S_REG_GZOFFSET     0x0A    // Z轴角速度偏移

// --- 数据转换系数 ---
#define JY901S_ACC_SCALE        (16.0f / 32768.0f)      // 加速度量程 ±16g
#define JY901S_GYRO_SCALE       (2000.0f / 32768.0f)    // 角速度量程 ±2000°/s
#define JY901S_ANGLE_SCALE      (180.0f / 32768.0f)     // 角度量程 ±180°
#define JY901S_MAG_SCALE        1.0f                     // 磁场强度
#define JY901S_TEMP_SCALE       (100.0f / 32768.0f)     // 温度量程

/**
 * @brief JY901S数据结构体
 */
typedef struct {
    // 原始数据
    int16_t acc_raw[3];         // 加速度原始数据 [X, Y, Z]
    int16_t gyro_raw[3];        // 角速度原始数据 [X, Y, Z]
    int16_t angle_raw[3];       // 角度原始数据 [Roll, Pitch, Yaw]
    int16_t mag_raw[3];         // 磁场原始数据 [X, Y, Z]
    int16_t temp_raw;           // 温度原始数据
    
    // 转换后的数据
    float acc[3];               // 加速度 (g) [X, Y, Z]
    float gyro[3];              // 角速度 (°/s) [X, Y, Z]
    float angle[3];             // 角度 (°) [Roll, Pitch, Yaw]
    float mag[3];               // 磁场强度 [X, Y, Z]
    float temperature;          // 温度 (°C)
    
    // 状态标志
    bool data_ready;            // 数据就绪标志
    uint32_t last_update_time;  // 上次更新时间戳
} JY901S_Data;

/**
 * @brief JY901S配置结构体
 */
typedef struct {
    uint8_t i2c_addr;           // I2C地址
    uint8_t update_rate;        // 数据更新率
    bool auto_calibration;      // 自动校准使能
} JY901S_Config;

// --- 移植接口函数类型定义 ---
/**
 * @brief I2C写函数类型
 * @param addr I2C设备地址
 * @param reg 寄存器地址
 * @param data 要写入的数据
 * @param len 数据长度
 * @return true-成功, false-失败
 */
typedef bool (*JY901S_I2C_Write_Func)(uint8_t addr, uint8_t reg, uint8_t *data, uint16_t len);

/**
 * @brief I2C读函数类型
 * @param addr I2C设备地址
 * @param reg 寄存器地址
 * @param data 读取数据缓冲区
 * @param len 数据长度
 * @return true-成功, false-失败
 */
typedef bool (*JY901S_I2C_Read_Func)(uint8_t addr, uint8_t reg, uint8_t *data, uint16_t len);

/**
 * @brief 延时函数类型
 * @param ms 延时毫秒数
 */
typedef void (*JY901S_Delay_Func)(uint32_t ms);

/**
 * @brief 获取系统时间函数类型
 * @return 系统时间戳 (ms)
 */
typedef uint32_t (*JY901S_GetTick_Func)(void);

/**
 * @brief JY901S驱动句柄
 */
typedef struct {
    JY901S_Config config;           // 配置参数
    JY901S_Data data;               // 数据
    
    // 移植接口函数
    JY901S_I2C_Write_Func i2c_write;
    JY901S_I2C_Read_Func i2c_read;
    JY901S_Delay_Func delay;
    JY901S_GetTick_Func get_tick;
    
    // 状态
    bool initialized;               // 初始化状态
} JY901S_Handle;

// --- 函数声明 ---

/**
 * @brief 初始化JY901S驱动
 * @param handle JY901S句柄
 * @param config 配置参数
 * @param i2c_write I2C写函数
 * @param i2c_read I2C读函数
 * @param delay 延时函数
 * @param get_tick 获取时间戳函数
 * @return true-成功, false-失败
 */
bool JY901S_Init(JY901S_Handle *handle, 
                 JY901S_Config *config,
                 JY901S_I2C_Write_Func i2c_write,
                 JY901S_I2C_Read_Func i2c_read,
                 JY901S_Delay_Func delay,
                 JY901S_GetTick_Func get_tick);

/**
 * @brief 读取所有传感器数据
 * @param handle JY901S句柄
 * @return true-成功, false-失败
 */
bool JY901S_ReadAllData(JY901S_Handle *handle);

/**
 * @brief 读取加速度数据
 * @param handle JY901S句柄
 * @return true-成功, false-失败
 */
bool JY901S_ReadAcceleration(JY901S_Handle *handle);

/**
 * @brief 读取角速度数据
 * @param handle JY901S句柄
 * @return true-成功, false-失败
 */
bool JY901S_ReadGyroscope(JY901S_Handle *handle);

/**
 * @brief 读取角度数据
 * @param handle JY901S句柄
 * @return true-成功, false-失败
 */
bool JY901S_ReadAngle(JY901S_Handle *handle);

/**
 * @brief 读取磁场数据
 * @param handle JY901S句柄
 * @return true-成功, false-失败
 */
bool JY901S_ReadMagnetometer(JY901S_Handle *handle);

/**
 * @brief 读取温度数据
 * @param handle JY901S句柄
 * @return true-成功, false-失败
 */
bool JY901S_ReadTemperature(JY901S_Handle *handle);

/**
 * @brief 校准传感器
 * @param handle JY901S句柄
 * @return true-成功, false-失败
 */
bool JY901S_Calibrate(JY901S_Handle *handle);

/**
 * @brief 复位传感器
 * @param handle JY901S句柄
 * @return true-成功, false-失败
 */
bool JY901S_Reset(JY901S_Handle *handle);

/**
 * @brief 设置数据输出频率
 * @param handle JY901S句柄
 * @param rate 输出频率 (0.2Hz, 0.5Hz, 1Hz, 2Hz, 5Hz, 10Hz, 20Hz, 50Hz, 100Hz, 200Hz)
 * @return true-成功, false-失败
 */
bool JY901S_SetOutputRate(JY901S_Handle *handle, uint8_t rate);

/**
 * @brief 获取Roll角度 (°)
 * @param handle JY901S句柄
 * @return Roll角度
 */
float JY901S_GetRoll(JY901S_Handle *handle);

/**
 * @brief 获取Pitch角度 (°)
 * @param handle JY901S句柄
 * @return Pitch角度
 */
float JY901S_GetPitch(JY901S_Handle *handle);

/**
 * @brief 获取Yaw角度 (°)
 * @param handle JY901S句柄
 * @return Yaw角度
 */
float JY901S_GetYaw(JY901S_Handle *handle);

#endif /* __JY901S_DRIVER_H__ */
