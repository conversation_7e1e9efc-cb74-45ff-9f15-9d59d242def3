#include "mpu6050_app.h"

float Pitch, Roll, Yaw; // pitch-�����ǣ���-��+����roll����ǣ�ǰ-��+����yaw-����ǣ���+��-����Ҫ���������ǵ�xyz�ῴ

void Mpu6050_Init(void)
{
    // 使用JY901S初始化替换原来的MPU6050
    if (JY901S_App_Init()) {
        Uart_Printf(&huart1, "JY901S initialized successfully (MPU6050 compatibility mode)\r\n");
    } else {
        Uart_Printf(&huart1, "JY901S initialization failed\r\n");
    }
}

void Mpu6050_Task(void)
{
    // 使用JY901S读取数据替换原来的MPU6050
    if (JY901S_App_Task()) {
        // 更新全局变量保持兼容性
        Pitch = JY901S_App_GetPitch();
        Roll = JY901S_App_GetRoll();
        Yaw = JY901S_App_GetYaw();

        // 如果需要连续角度转换，可以添加类似的函数
        // Yaw = convert_to_continuous_yaw(Yaw);
    }
}
