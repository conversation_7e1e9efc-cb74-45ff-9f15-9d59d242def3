/**
 * @file jy901s_test.c
 * @brief JY901S陀螺仪测试程序
 * @note 用于验证JY901S驱动是否正常工作
 */

#include "MyDefine.h"
#include "jy901s_app.h"

// 测试状态枚举
typedef enum {
    TEST_INIT = 0,
    TEST_READ_DATA,
    TEST_CALIBRATE,
    TEST_RESET,
    TEST_COMPLETE
} JY901S_Test_State;

static JY901S_Test_State test_state = TEST_INIT;
static uint32_t test_timer = 0;

/**
 * @brief JY901S测试主函数
 * @note 在main函数的while循环中调用此函数
 */
void JY901S_Test_Main(void)
{
    static uint32_t last_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 每100ms执行一次测试
    if (current_time - last_time < 100) {
        return;
    }
    last_time = current_time;
    
    switch (test_state) {
        case TEST_INIT:
            JY901S_Test_Init();
            break;
            
        case TEST_READ_DATA:
            JY901S_Test_ReadData();
            break;
            
        case TEST_CALIBRATE:
            JY901S_Test_Calibrate();
            break;
            
        case TEST_RESET:
            JY901S_Test_Reset();
            break;
            
        case TEST_COMPLETE:
            JY901S_Test_Complete();
            break;
            
        default:
            test_state = TEST_INIT;
            break;
    }
}

/**
 * @brief 测试初始化
 */
void JY901S_Test_Init(void)
{
    Uart_Printf(&huart1, "\r\n=== JY901S Driver Test Start ===\r\n");
    
    // 初始化JY901S
    if (JY901S_App_Init()) {
        Uart_Printf(&huart1, "✓ JY901S initialization successful\r\n");
        test_state = TEST_READ_DATA;
        test_timer = 0;
    } else {
        Uart_Printf(&huart1, "✗ JY901S initialization failed\r\n");
        Uart_Printf(&huart1, "Please check:\r\n");
        Uart_Printf(&huart1, "1. I2C wiring (SDA, SCL)\r\n");
        Uart_Printf(&huart1, "2. Power supply (3.3V or 5V)\r\n");
        Uart_Printf(&huart1, "3. I2C address (default 0x50)\r\n");
        
        // 重试初始化
        HAL_Delay(2000);
    }
}

/**
 * @brief 测试数据读取
 */
void JY901S_Test_ReadData(void)
{
    test_timer++;
    
    // 读取传感器数据
    if (JY901S_App_Task()) {
        // 每10次循环 (1秒) 打印一次数据
        if (test_timer % 10 == 0) {
            Uart_Printf(&huart1, "\r\n--- Data Read Test (%.1fs) ---\r\n", test_timer * 0.1f);
            JY901S_App_PrintData();
            
            // 检查数据合理性
            JY901S_Test_CheckDataValidity();
        }
        
        // 读取30秒后进入校准测试
        if (test_timer >= 300) {
            test_state = TEST_CALIBRATE;
            test_timer = 0;
        }
    } else {
        Uart_Printf(&huart1, "✗ Data read failed at %.1fs\r\n", test_timer * 0.1f);
    }
}

/**
 * @brief 测试校准功能
 */
void JY901S_Test_Calibrate(void)
{
    if (test_timer == 0) {
        Uart_Printf(&huart1, "\r\n--- Calibration Test ---\r\n");
        Uart_Printf(&huart1, "Please keep the sensor stationary...\r\n");
        
        if (JY901S_App_Calibrate()) {
            Uart_Printf(&huart1, "✓ Calibration command sent successfully\r\n");
        } else {
            Uart_Printf(&huart1, "✗ Calibration command failed\r\n");
        }
    }
    
    test_timer++;
    
    // 校准需要约3秒，等待5秒后进入复位测试
    if (test_timer >= 50) {
        test_state = TEST_RESET;
        test_timer = 0;
    }
}

/**
 * @brief 测试复位功能
 */
void JY901S_Test_Reset(void)
{
    if (test_timer == 0) {
        Uart_Printf(&huart1, "\r\n--- Reset Test ---\r\n");
        
        if (JY901S_App_Reset()) {
            Uart_Printf(&huart1, "✓ Reset command sent successfully\r\n");
        } else {
            Uart_Printf(&huart1, "✗ Reset command failed\r\n");
        }
    }
    
    test_timer++;
    
    // 等待2秒后完成测试
    if (test_timer >= 20) {
        test_state = TEST_COMPLETE;
        test_timer = 0;
    }
}

/**
 * @brief 测试完成
 */
void JY901S_Test_Complete(void)
{
    if (test_timer == 0) {
        Uart_Printf(&huart1, "\r\n=== JY901S Driver Test Complete ===\r\n");
        Uart_Printf(&huart1, "Test Summary:\r\n");
        Uart_Printf(&huart1, "✓ Initialization test\r\n");
        Uart_Printf(&huart1, "✓ Data reading test (30s)\r\n");
        Uart_Printf(&huart1, "✓ Calibration test\r\n");
        Uart_Printf(&huart1, "✓ Reset test\r\n");
        Uart_Printf(&huart1, "\r\nJY901S driver is ready for use!\r\n");
        Uart_Printf(&huart1, "You can now integrate it into your control system.\r\n");
    }
    
    test_timer++;
    
    // 每10秒打印一次实时数据
    if (test_timer % 100 == 0) {
        Uart_Printf(&huart1, "\r\n--- Real-time Data ---\r\n");
        JY901S_App_Task();
        JY901S_App_PrintData();
    }
}

/**
 * @brief 检查数据有效性
 */
void JY901S_Test_CheckDataValidity(void)
{
    float roll = JY901S_App_GetRoll();
    float pitch = JY901S_App_GetPitch();
    float yaw = JY901S_App_GetYaw();
    float acc_z = JY901S_App_GetAccZ();
    float temp = JY901S_App_GetTemperature();
    
    bool data_valid = true;
    
    // 检查角度范围
    if (roll < -180.0f || roll > 180.0f) {
        Uart_Printf(&huart1, "⚠ Warning: Roll angle out of range (%.1f°)\r\n", roll);
        data_valid = false;
    }
    
    if (pitch < -180.0f || pitch > 180.0f) {
        Uart_Printf(&huart1, "⚠ Warning: Pitch angle out of range (%.1f°)\r\n", pitch);
        data_valid = false;
    }
    
    if (yaw < -180.0f || yaw > 180.0f) {
        Uart_Printf(&huart1, "⚠ Warning: Yaw angle out of range (%.1f°)\r\n", yaw);
        data_valid = false;
    }
    
    // 检查Z轴加速度 (静止时应该接近1g)
    if (acc_z < 0.5f || acc_z > 1.5f) {
        Uart_Printf(&huart1, "⚠ Warning: Z-axis acceleration unusual (%.2fg)\r\n", acc_z);
        data_valid = false;
    }
    
    // 检查温度范围
    if (temp < -40.0f || temp > 85.0f) {
        Uart_Printf(&huart1, "⚠ Warning: Temperature out of range (%.1f°C)\r\n", temp);
        data_valid = false;
    }
    
    if (data_valid) {
        Uart_Printf(&huart1, "✓ All data within valid ranges\r\n");
    }
}

/**
 * @brief 简单的性能测试
 */
void JY901S_Test_Performance(void)
{
    uint32_t start_time, end_time;
    uint32_t read_time_us;
    
    Uart_Printf(&huart1, "\r\n--- Performance Test ---\r\n");
    
    // 测试读取时间
    start_time = HAL_GetTick() * 1000 + (SysTick->LOAD - SysTick->VAL) / (SystemCoreClock / 1000000);
    JY901S_App_Task();
    end_time = HAL_GetTick() * 1000 + (SysTick->LOAD - SysTick->VAL) / (SystemCoreClock / 1000000);
    
    read_time_us = end_time - start_time;
    
    Uart_Printf(&huart1, "Data read time: %lu μs\r\n", read_time_us);
    
    if (read_time_us < 1000) {
        Uart_Printf(&huart1, "✓ Performance: Excellent (< 1ms)\r\n");
    } else if (read_time_us < 5000) {
        Uart_Printf(&huart1, "✓ Performance: Good (< 5ms)\r\n");
    } else {
        Uart_Printf(&huart1, "⚠ Performance: Slow (> 5ms)\r\n");
    }
}

/**
 * @brief 连续性测试
 */
void JY901S_Test_Continuity(void)
{
    static uint32_t success_count = 0;
    static uint32_t fail_count = 0;
    static uint32_t total_count = 0;
    
    total_count++;
    
    if (JY901S_App_Task()) {
        success_count++;
    } else {
        fail_count++;
    }
    
    // 每1000次测试打印一次统计
    if (total_count % 1000 == 0) {
        float success_rate = (float)success_count / total_count * 100.0f;
        Uart_Printf(&huart1, "Continuity Test: %lu/%lu (%.1f%% success)\r\n", 
                   success_count, total_count, success_rate);
        
        if (success_rate > 99.0f) {
            Uart_Printf(&huart1, "✓ Excellent reliability\r\n");
        } else if (success_rate > 95.0f) {
            Uart_Printf(&huart1, "✓ Good reliability\r\n");
        } else {
            Uart_Printf(&huart1, "⚠ Poor reliability - check connections\r\n");
        }
    }
}
