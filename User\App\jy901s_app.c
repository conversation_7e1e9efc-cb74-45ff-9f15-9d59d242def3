#include "jy901s_app.h"

// JY901S应用层实例
JY901S_STM32_Handle jy901s_handle;

/**
 * @brief 初始化JY901S应用
 */
bool JY901S_App_Init(void)
{
    // 初始化JY901S传感器
    // 注意：这里的I2C句柄需要根据您的实际硬件连接进行修改
    bool result = JY901S_STM32_Init(&jy901s_handle,
                                    &hi2c1,         // I2C句柄 (根据实际情况修改)
                                    JY901S_I2C_ADDR, // 设备地址 0x50
                                    1000,           // 超时时间 1000ms
                                    false);         // 不自动校准 (可根据需要修改)
    
    if (result) {
        Uart_Printf(&huart1, "=== JY901S Initialized Successfully ===\r\n");
        Uart_Printf(&huart1, "Device Address: 0x%02X\r\n", JY901S_I2C_ADDR);
        Uart_Printf(&huart1, "I2C Timeout: 1000ms\r\n");
        
        // 读取一次数据验证通信
        if (JY901S_STM32_ReadAllData(&jy901s_handle)) {
            Uart_Printf(&huart1, "Initial data read successful\r\n");
            JY901S_App_PrintData();
        } else {
            Uart_Printf(&huart1, "Warning: Initial data read failed\r\n");
        }
    } else {
        Uart_Printf(&huart1, "Error: JY901S initialization failed\r\n");
    }
    
    return result;
}

/**
 * @brief JY901S应用任务 (需要周期性调用)
 */
bool JY901S_App_Task(void)
{
    // 读取所有传感器数据
    bool result = JY901S_STM32_ReadAllData(&jy901s_handle);
    
    if (!result) {
        // 可选：记录错误或重试
        static uint16_t error_count = 0;
        error_count++;
        if (error_count >= 100) { // 每500ms打印一次错误 (5ms * 100)
            error_count = 0;
            Uart_Printf(&huart1, "JY901S read error\r\n");
        }
    }
    
    // 可选：打印调试信息 (取消注释以启用)
    /*
    static uint16_t debug_counter = 0;
    if (++debug_counter >= 200) { // 每1秒打印一次 (5ms * 200 = 1s)
        debug_counter = 0;
        if (result) {
            JY901S_App_PrintData();
        }
    }
    */
    
    return result;
}

/**
 * @brief 获取Roll角度 (横滚角)
 */
float JY901S_App_GetRoll(void)
{
    return JY901S_STM32_GetRoll(&jy901s_handle);
}

/**
 * @brief 获取Pitch角度 (俯仰角)
 */
float JY901S_App_GetPitch(void)
{
    return JY901S_STM32_GetPitch(&jy901s_handle);
}

/**
 * @brief 获取Yaw角度 (偏航角)
 */
float JY901S_App_GetYaw(void)
{
    return JY901S_STM32_GetYaw(&jy901s_handle);
}

/**
 * @brief 获取X轴加速度
 */
float JY901S_App_GetAccX(void)
{
    return jy901s_handle.jy901s.data.acc[0];
}

/**
 * @brief 获取Y轴加速度
 */
float JY901S_App_GetAccY(void)
{
    return jy901s_handle.jy901s.data.acc[1];
}

/**
 * @brief 获取Z轴加速度
 */
float JY901S_App_GetAccZ(void)
{
    return jy901s_handle.jy901s.data.acc[2];
}

/**
 * @brief 获取X轴角速度
 */
float JY901S_App_GetGyroX(void)
{
    return jy901s_handle.jy901s.data.gyro[0];
}

/**
 * @brief 获取Y轴角速度
 */
float JY901S_App_GetGyroY(void)
{
    return jy901s_handle.jy901s.data.gyro[1];
}

/**
 * @brief 获取Z轴角速度
 */
float JY901S_App_GetGyroZ(void)
{
    return jy901s_handle.jy901s.data.gyro[2];
}

/**
 * @brief 获取温度
 */
float JY901S_App_GetTemperature(void)
{
    return JY901S_STM32_GetTemperature(&jy901s_handle);
}

/**
 * @brief 校准传感器
 */
bool JY901S_App_Calibrate(void)
{
    Uart_Printf(&huart1, "JY901S calibration started...\r\n");
    bool result = JY901S_STM32_Calibrate(&jy901s_handle);
    if (result) {
        Uart_Printf(&huart1, "JY901S calibration completed\r\n");
    } else {
        Uart_Printf(&huart1, "JY901S calibration failed\r\n");
    }
    return result;
}

/**
 * @brief 复位传感器
 */
bool JY901S_App_Reset(void)
{
    Uart_Printf(&huart1, "JY901S reset...\r\n");
    bool result = JY901S_STM32_Reset(&jy901s_handle);
    if (result) {
        Uart_Printf(&huart1, "JY901S reset completed\r\n");
    } else {
        Uart_Printf(&huart1, "JY901S reset failed\r\n");
    }
    return result;
}

/**
 * @brief 检查数据是否就绪
 */
bool JY901S_App_IsDataReady(void)
{
    return jy901s_handle.jy901s.data.data_ready;
}

/**
 * @brief 获取上次更新时间
 */
uint32_t JY901S_App_GetLastUpdateTime(void)
{
    return jy901s_handle.jy901s.data.last_update_time;
}

/**
 * @brief 打印传感器数据 (调试用)
 */
void JY901S_App_PrintData(void)
{
    if (!JY901S_App_IsDataReady()) {
        Uart_Printf(&huart1, "JY901S: Data not ready\r\n");
        return;
    }
    
    Uart_Printf(&huart1, "=== JY901S Data ===\r\n");
    
    // 角度数据
    Uart_Printf(&huart1, "Angle: R=%.1f° P=%.1f° Y=%.1f°\r\n",
               JY901S_App_GetRoll(),
               JY901S_App_GetPitch(),
               JY901S_App_GetYaw());
    
    // 加速度数据
    Uart_Printf(&huart1, "Acc: X=%.2fg Y=%.2fg Z=%.2fg\r\n",
               JY901S_App_GetAccX(),
               JY901S_App_GetAccY(),
               JY901S_App_GetAccZ());
    
    // 角速度数据
    Uart_Printf(&huart1, "Gyro: X=%.1f°/s Y=%.1f°/s Z=%.1f°/s\r\n",
               JY901S_App_GetGyroX(),
               JY901S_App_GetGyroY(),
               JY901S_App_GetGyroZ());
    
    // 温度数据
    Uart_Printf(&huart1, "Temp: %.1f°C\r\n", JY901S_App_GetTemperature());
    
    Uart_Printf(&huart1, "==================\r\n");
}
