#ifndef __MG513X_DRIVER_H__
#define __MG513X_DRIVER_H__

#include "MyDefine.h"

// --- MG513X编码电机参数配置 ---
// 编码器参数
#define MG513X_ENCODER_PPR (11 * 30 * 4)  // 11线/相, 30倍减速比, 4倍频 = 1320
#define MG513X_WHEEL_DIAMETER_CM 6.5f     // 常用轮径 (可根据实际情况修改)
#define MG513X_SAMPLING_TIME_S 0.005f     // 采样时间 5ms

// 自动计算参数
#define PI 3.14159265f
#define MG513X_WHEEL_CIRCUMFERENCE_CM (MG513X_WHEEL_DIAMETER_CM * PI)

// 电机控制参数
#define MG513X_MAX_SPEED 999              // 最大PWM值
#define MG513X_MIN_SPEED -999             // 最小PWM值
#define MG513X_DEFAULT_DEAD_BAND 80       // 默认死区补偿

/**
 * @brief MG513X编码电机配置结构体
 */
typedef struct {
    // PWM控制
    TIM_HandleTypeDef *pwm_htim;          // PWM定时器
    uint32_t pwm_channel;                 // PWM通道
    
    // 方向控制引脚
    struct {
        GPIO_TypeDef *port;
        uint16_t pin;
    } dir1, dir2;                         // 方向控制引脚
    
    // 编码器
    TIM_HandleTypeDef *encoder_htim;      // 编码器定时器
    
    // 配置参数
    uint8_t motor_reverse;                // 电机方向反转 0-正常 1-反转
    uint8_t encoder_reverse;              // 编码器方向反转 0-正常 1-反转
    int dead_band_speed;                  // 死区补偿值
} MG513X_Config;

/**
 * @brief MG513X编码电机数据结构体
 */
typedef struct {
    MG513X_Config config;                 // 配置信息
    
    // 控制变量
    int target_speed;                     // 目标速度 (PWM值)
    int current_speed;                    // 当前速度 (PWM值)
    
    // 编码器数据
    int16_t encoder_count;                // 当前周期编码器计数
    int32_t encoder_total_count;          // 编码器累计计数
    float speed_cm_s;                     // 实际速度 (cm/s)
    float speed_rpm;                      // 实际转速 (RPM)
    
    // 状态标志
    uint8_t is_running;                   // 运行状态
    uint8_t direction;                    // 当前方向 0-正转 1-反转
} MG513X_Motor;

// --- 函数声明 ---

/**
 * @brief 初始化MG513X编码电机
 * @param motor 电机结构体指针
 * @param pwm_htim PWM定时器
 * @param pwm_channel PWM通道
 * @param dir1_port 方向控制引脚1端口
 * @param dir1_pin 方向控制引脚1
 * @param dir2_port 方向控制引脚2端口  
 * @param dir2_pin 方向控制引脚2
 * @param encoder_htim 编码器定时器
 * @param motor_reverse 电机方向反转
 * @param encoder_reverse 编码器方向反转
 * @param dead_band_speed 死区补偿值
 */
void MG513X_Init(MG513X_Motor* motor, 
                 TIM_HandleTypeDef *pwm_htim, uint32_t pwm_channel,
                 GPIO_TypeDef *dir1_port, uint16_t dir1_pin,
                 GPIO_TypeDef *dir2_port, uint16_t dir2_pin,
                 TIM_HandleTypeDef *encoder_htim,
                 uint8_t motor_reverse, uint8_t encoder_reverse,
                 int dead_band_speed);

/**
 * @brief 设置电机速度
 * @param motor 电机结构体指针
 * @param speed 目标速度 (-999 ~ 999)
 */
void MG513X_SetSpeed(MG513X_Motor* motor, int speed);

/**
 * @brief 更新编码器数据 (需要周期性调用)
 * @param motor 电机结构体指针
 */
void MG513X_UpdateEncoder(MG513X_Motor* motor);

/**
 * @brief 电机停止 (自由停止)
 * @param motor 电机结构体指针
 */
void MG513X_Stop(MG513X_Motor* motor);

/**
 * @brief 电机刹车 (短路刹车)
 * @param motor 电机结构体指针
 */
void MG513X_Brake(MG513X_Motor* motor);

/**
 * @brief 获取电机实际速度 (cm/s)
 * @param motor 电机结构体指针
 * @return 速度值 (cm/s)
 */
float MG513X_GetSpeedCmS(MG513X_Motor* motor);

/**
 * @brief 获取电机实际转速 (RPM)
 * @param motor 电机结构体指针
 * @return 转速值 (RPM)
 */
float MG513X_GetSpeedRPM(MG513X_Motor* motor);

/**
 * @brief 获取编码器总计数
 * @param motor 电机结构体指针
 * @return 总计数值
 */
int32_t MG513X_GetTotalCount(MG513X_Motor* motor);

/**
 * @brief 重置编码器计数
 * @param motor 电机结构体指针
 */
void MG513X_ResetEncoder(MG513X_Motor* motor);

#endif /* __MG513X_DRIVER_H__ */
