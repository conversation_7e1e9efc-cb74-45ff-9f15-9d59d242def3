/**
 * @file jy901s_integration_example.c
 * @brief JY901S陀螺仪I2C驱动集成示例
 * @note 这个文件展示了如何在您的项目中集成JY901S驱动
 */

#include "MyDefine.h"
#include "jy901s_app.h"

/**
 * @brief 在 Scheduler_Task.c 的 System_Init() 函数中添加初始化
 */
void System_Init_Example(void)
{
    // 原有的初始化代码...
    Led_Init();
    Key_Init();
    Oled_Init();
    Uart_Init();
    Gray_Init();
    Motor_Init();
    Encoder_Init();
    
    // 添加JY901S初始化 (替换原来的 Mpu6050_Init())
    JY901S_App_Init();
    
    PID_Init();
    Uart_Printf(&huart1, "=== System Init ===\r\n");
    HAL_TIM_Base_Start_IT(&htim2);
}

/**
 * @brief 在 Scheduler_Task.c 的定时器中断中添加JY901S任务
 */
void HAL_TIM_PeriodElapsedCallback_Example(TIM_HandleTypeDef *htim)
{
    if(htim->Instance != htim2.Instance) return;
    
    /* 10ms 按键扫描*/
    if(++key_timer10ms >= 10)
    {
        key_timer10ms = 0;
        Key_Task();
    }
    
    /* 5ms 控制周期*/
    if(++measure_timer5ms >= 5) 
    {
        measure_timer5ms = 0;
        Encoder_Task();
        
        // 使用JY901S任务 (替换原来的 Mpu6050_Task())
        JY901S_App_Task();
        
        Gray_Task();
        PID_Task();
    }
    
    // 其他代码保持不变...
}

/**
 * @brief 在 pid_app.c 中修改PID任务以使用JY901S数据
 */
void PID_Task_Example(void)
{
    if(pid_running == false) return;
    
    int output_left = 0, output_right = 0;
    
    if(pid_control_mode == 0) // 角度环控制
    {
        if(distance > 20000) // 超过指定距离时减速
            basic_speed = 75;
        else
            basic_speed = 130;
        
        // 使用JY901S的Yaw角度进行角度环控制
        float current_yaw = JY901S_App_GetYaw();
        
        // 角度环PID计算
        output_left = pid_calculate_positional(&pid_angle, current_yaw);
        output_right = -output_left; // 差速控制
        
        // 叠加基础速度
        output_left += basic_speed;
        output_right += basic_speed;
    }
    else // 循迹环控制
    {
        basic_speed = 94;
        Line_PID_control();
        
        // 可选：使用JY901S的角速度进行姿态稳定
        float gyro_z = JY901S_App_GetGyroZ();
        int gyro_compensation = (int)(gyro_z * 0.5f); // 角速度补偿系数
        
        output_left = left_motor.target_speed - gyro_compensation;
        output_right = right_motor.target_speed + gyro_compensation;
    }
    
    // 输出限幅
    output_left = pid_constrain(output_left, -999, 999);
    output_right = pid_constrain(output_right, -999, 999);
    
    // 设置电机速度
    Motor_Set_Speed(&left_motor, output_left);
    Motor_Set_Speed(&right_motor, output_right);
}

/**
 * @brief 在 MyDefine.h 中添加头文件包含
 */
/*
在 MyDefine.h 的驱动层头文件部分添加：
#include "jy901s_driver.h"
#include "jy901s_stm32_port.h"

在应用层头文件部分添加：
#include "jy901s_app.h"
*/

/**
 * @brief 调试和测试函数示例
 */
void JY901S_Debug_Example(void)
{
    // 检查数据是否就绪
    if (!JY901S_App_IsDataReady()) {
        Uart_Printf(&huart1, "JY901S data not ready\r\n");
        return;
    }
    
    // 获取各种传感器数据
    float roll = JY901S_App_GetRoll();
    float pitch = JY901S_App_GetPitch();
    float yaw = JY901S_App_GetYaw();
    
    float acc_x = JY901S_App_GetAccX();
    float acc_y = JY901S_App_GetAccY();
    float acc_z = JY901S_App_GetAccZ();
    
    float gyro_x = JY901S_App_GetGyroX();
    float gyro_y = JY901S_App_GetGyroY();
    float gyro_z = JY901S_App_GetGyroZ();
    
    float temperature = JY901S_App_GetTemperature();
    
    // 打印所有数据
    JY901S_App_PrintData();
    
    // 校准传感器 (如果需要)
    // JY901S_App_Calibrate();
    
    // 复位传感器 (如果需要)
    // JY901S_App_Reset();
}

/**
 * @brief 按键控制示例 (在key_app.c中添加)
 */
void Key_JY901S_Control_Example(void)
{
    static uint8_t key_state = 0;
    
    if (Key_Scan() == KEY_PRESS) {
        key_state++;
        if (key_state > 3) key_state = 0;
        
        switch (key_state) {
            case 0:
                Uart_Printf(&huart1, "JY901S: Normal mode\r\n");
                break;
            case 1:
                Uart_Printf(&huart1, "JY901S: Print data\r\n");
                JY901S_App_PrintData();
                break;
            case 2:
                Uart_Printf(&huart1, "JY901S: Calibrate\r\n");
                JY901S_App_Calibrate();
                break;
            case 3:
                Uart_Printf(&huart1, "JY901S: Reset\r\n");
                JY901S_App_Reset();
                break;
        }
    }
}

/**
 * @brief OLED显示示例 (在oled_app.c中添加)
 */
void Oled_JY901S_Display_Example(void)
{
    char str[20];
    
    // 显示角度信息
    sprintf(str, "R:%.1f", JY901S_App_GetRoll());
    Oled_Show_String(0, 0, str, 12);
    
    sprintf(str, "P:%.1f", JY901S_App_GetPitch());
    Oled_Show_String(0, 12, str, 12);
    
    sprintf(str, "Y:%.1f", JY901S_App_GetYaw());
    Oled_Show_String(0, 24, str, 12);
    
    // 显示温度
    sprintf(str, "T:%.1fC", JY901S_App_GetTemperature());
    Oled_Show_String(0, 36, str, 12);
    
    // 显示数据状态
    if (JY901S_App_IsDataReady()) {
        Oled_Show_String(0, 48, "JY901S:OK", 12);
    } else {
        Oled_Show_String(0, 48, "JY901S:ERR", 12);
    }
}

/**
 * @brief 配置说明
 */
/*
硬件连接说明：
1. JY901S的VCC连接到3.3V或5V电源
2. JY901S的GND连接到地
3. JY901S的SDA连接到STM32的I2C_SDA引脚 (如PB7)
4. JY901S的SCL连接到STM32的I2C_SCL引脚 (如PB6)
5. 在STM32CubeMX中配置I2C1为标准模式，速度100kHz

软件配置说明：
1. 确保I2C时钟配置正确 (通常100kHz)
2. 根据实际I2C接口修改 jy901s_app.c 中的 &hi2c1
3. 根据需要调整设备地址 (默认0x50)
4. 根据需要启用/禁用自动校准

移植到其他平台：
1. 只需要修改 jy901s_stm32_port.c 中的I2C接口函数
2. 核心驱动 jy901s_driver.c 无需修改
3. 应用层接口保持不变

使用步骤：
1. 将所有JY901S相关文件添加到项目
2. 在 MyDefine.h 中包含相应头文件
3. 在 System_Init() 中调用 JY901S_App_Init()
4. 在定时器中断中调用 JY901S_App_Task()
5. 在控制算法中使用JY901S数据
*/
