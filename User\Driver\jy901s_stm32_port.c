#include "jy901s_stm32_port.h"

// 全局变量用于移植接口
JY901S_STM32_Handle *g_jy901s_stm32_handle = NULL;

// --- STM32平台接口函数实现 ---

/**
 * @brief STM32平台I2C写函数
 */
bool JY901S_STM32_I2C_Write(uint8_t addr, uint8_t reg, uint8_t *data, uint16_t len)
{
    if (!g_jy901s_stm32_handle) {
        return false;
    }
    
    HAL_StatusTypeDef status;
    
    // 使用HAL_I2C_Mem_Write进行寄存器写入
    status = HAL_I2C_Mem_Write(g_jy901s_stm32_handle->stm32_config.hi2c,
                               addr << 1,  // 转换为8位地址
                               reg,
                               I2C_MEMADD_SIZE_8BIT,
                               data,
                               len,
                               g_jy901s_stm32_handle->stm32_config.timeout_ms);
    
    return (status == HAL_OK);
}

/**
 * @brief STM32平台I2C读函数
 */
bool JY901S_STM32_I2C_Read(uint8_t addr, uint8_t reg, uint8_t *data, uint16_t len)
{
    if (!g_jy901s_stm32_handle) {
        return false;
    }
    
    HAL_StatusTypeDef status;
    
    // 使用HAL_I2C_Mem_Read进行寄存器读取
    status = HAL_I2C_Mem_Read(g_jy901s_stm32_handle->stm32_config.hi2c,
                              addr << 1,  // 转换为8位地址
                              reg,
                              I2C_MEMADD_SIZE_8BIT,
                              data,
                              len,
                              g_jy901s_stm32_handle->stm32_config.timeout_ms);
    
    return (status == HAL_OK);
}

/**
 * @brief STM32平台延时函数
 */
void JY901S_STM32_Delay(uint32_t ms)
{
    HAL_Delay(ms);
}

/**
 * @brief STM32平台获取系统时间函数
 */
uint32_t JY901S_STM32_GetTick(void)
{
    return HAL_GetTick();
}

// --- 高级封装函数实现 ---

/**
 * @brief 初始化STM32平台的JY901S
 */
bool JY901S_STM32_Init(JY901S_STM32_Handle *handle, 
                       I2C_HandleTypeDef *hi2c,
                       uint8_t device_addr,
                       uint32_t timeout_ms,
                       bool auto_calibration)
{
    if (!handle || !hi2c) {
        return false;
    }
    
    // 配置STM32特定参数
    handle->stm32_config.hi2c = hi2c;
    handle->stm32_config.timeout_ms = timeout_ms;
    handle->stm32_config.device_addr = device_addr;
    
    // 设置全局句柄用于移植接口
    g_jy901s_stm32_handle = handle;
    
    // 配置JY901S通用参数
    JY901S_Config config = {
        .i2c_addr = device_addr,
        .update_rate = 10,  // 10Hz默认更新率
        .auto_calibration = auto_calibration
    };
    
    // 初始化JY901S驱动
    return JY901S_Init(&handle->jy901s,
                       &config,
                       JY901S_STM32_I2C_Write,
                       JY901S_STM32_I2C_Read,
                       JY901S_STM32_Delay,
                       JY901S_STM32_GetTick);
}

/**
 * @brief 读取所有传感器数据 (STM32封装)
 */
bool JY901S_STM32_ReadAllData(JY901S_STM32_Handle *handle)
{
    if (!handle) {
        return false;
    }
    
    return JY901S_ReadAllData(&handle->jy901s);
}

/**
 * @brief 获取Roll角度 (STM32封装)
 */
float JY901S_STM32_GetRoll(JY901S_STM32_Handle *handle)
{
    if (!handle) {
        return 0.0f;
    }
    
    return JY901S_GetRoll(&handle->jy901s);
}

/**
 * @brief 获取Pitch角度 (STM32封装)
 */
float JY901S_STM32_GetPitch(JY901S_STM32_Handle *handle)
{
    if (!handle) {
        return 0.0f;
    }
    
    return JY901S_GetPitch(&handle->jy901s);
}

/**
 * @brief 获取Yaw角度 (STM32封装)
 */
float JY901S_STM32_GetYaw(JY901S_STM32_Handle *handle)
{
    if (!handle) {
        return 0.0f;
    }
    
    return JY901S_GetYaw(&handle->jy901s);
}

/**
 * @brief 获取加速度数据 (STM32封装)
 */
bool JY901S_STM32_GetAcceleration(JY901S_STM32_Handle *handle, float *acc_x, float *acc_y, float *acc_z)
{
    if (!handle || !acc_x || !acc_y || !acc_z) {
        return false;
    }
    
    *acc_x = handle->jy901s.data.acc[0];
    *acc_y = handle->jy901s.data.acc[1];
    *acc_z = handle->jy901s.data.acc[2];
    
    return true;
}

/**
 * @brief 获取角速度数据 (STM32封装)
 */
bool JY901S_STM32_GetGyroscope(JY901S_STM32_Handle *handle, float *gyro_x, float *gyro_y, float *gyro_z)
{
    if (!handle || !gyro_x || !gyro_y || !gyro_z) {
        return false;
    }
    
    *gyro_x = handle->jy901s.data.gyro[0];
    *gyro_y = handle->jy901s.data.gyro[1];
    *gyro_z = handle->jy901s.data.gyro[2];
    
    return true;
}

/**
 * @brief 获取温度数据 (STM32封装)
 */
float JY901S_STM32_GetTemperature(JY901S_STM32_Handle *handle)
{
    if (!handle) {
        return 0.0f;
    }
    
    return handle->jy901s.data.temperature;
}

/**
 * @brief 校准传感器 (STM32封装)
 */
bool JY901S_STM32_Calibrate(JY901S_STM32_Handle *handle)
{
    if (!handle) {
        return false;
    }
    
    return JY901S_Calibrate(&handle->jy901s);
}

/**
 * @brief 复位传感器 (STM32封装)
 */
bool JY901S_STM32_Reset(JY901S_STM32_Handle *handle)
{
    if (!handle) {
        return false;
    }
    
    return JY901S_Reset(&handle->jy901s);
}
