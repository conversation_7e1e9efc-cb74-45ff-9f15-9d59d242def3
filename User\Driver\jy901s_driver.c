#include "jy901s_driver.h"

// 内部辅助函数声明
static void JY901S_ConvertRawData(JY901S_Handle *handle);
static bool JY901S_WriteRegister(JY901S_Handle *handle, uint8_t reg, uint8_t value);
static bool JY901S_ReadRegisters(JY901S_Handle *handle, uint8_t reg, uint8_t *data, uint16_t len);

/**
 * @brief 初始化JY901S驱动
 */
bool JY901S_Init(JY901S_Handle *handle, 
                 JY901S_Config *config,
                 JY901S_I2C_Write_Func i2c_write,
                 JY901S_I2C_Read_Func i2c_read,
                 JY901S_Delay_Func delay,
                 JY901S_GetTick_Func get_tick)
{
    if (!handle || !config || !i2c_write || !i2c_read || !delay || !get_tick) {
        return false;
    }
    
    // 保存配置和接口函数
    handle->config = *config;
    handle->i2c_write = i2c_write;
    handle->i2c_read = i2c_read;
    handle->delay = delay;
    handle->get_tick = get_tick;
    
    // 初始化数据结构
    for (int i = 0; i < 3; i++) {
        handle->data.acc_raw[i] = 0;
        handle->data.gyro_raw[i] = 0;
        handle->data.angle_raw[i] = 0;
        handle->data.mag_raw[i] = 0;
        handle->data.acc[i] = 0.0f;
        handle->data.gyro[i] = 0.0f;
        handle->data.angle[i] = 0.0f;
        handle->data.mag[i] = 0.0f;
    }
    handle->data.temp_raw = 0;
    handle->data.temperature = 0.0f;
    handle->data.data_ready = false;
    handle->data.last_update_time = 0;
    
    // 延时等待传感器启动
    handle->delay(100);
    
    // 尝试读取一次数据验证通信
    uint8_t test_data[6];
    if (!JY901S_ReadRegisters(handle, JY901S_REG_ACC, test_data, 6)) {
        return false;
    }
    
    // 如果启用自动校准，执行校准
    if (handle->config.auto_calibration) {
        JY901S_Calibrate(handle);
    }
    
    handle->initialized = true;
    return true;
}

/**
 * @brief 读取所有传感器数据
 */
bool JY901S_ReadAllData(JY901S_Handle *handle)
{
    if (!handle || !handle->initialized) {
        return false;
    }
    
    bool success = true;
    
    // 读取加速度数据
    success &= JY901S_ReadAcceleration(handle);
    
    // 读取角速度数据
    success &= JY901S_ReadGyroscope(handle);
    
    // 读取角度数据
    success &= JY901S_ReadAngle(handle);
    
    // 读取磁场数据
    success &= JY901S_ReadMagnetometer(handle);
    
    // 读取温度数据
    success &= JY901S_ReadTemperature(handle);
    
    if (success) {
        // 转换原始数据
        JY901S_ConvertRawData(handle);
        handle->data.data_ready = true;
        handle->data.last_update_time = handle->get_tick();
    }
    
    return success;
}

/**
 * @brief 读取加速度数据
 */
bool JY901S_ReadAcceleration(JY901S_Handle *handle)
{
    if (!handle || !handle->initialized) {
        return false;
    }
    
    uint8_t data[6];
    if (!JY901S_ReadRegisters(handle, JY901S_REG_ACC, data, 6)) {
        return false;
    }
    
    // 组合16位数据 (小端序)
    handle->data.acc_raw[0] = (int16_t)((data[1] << 8) | data[0]);  // X轴
    handle->data.acc_raw[1] = (int16_t)((data[3] << 8) | data[2]);  // Y轴
    handle->data.acc_raw[2] = (int16_t)((data[5] << 8) | data[4]);  // Z轴
    
    // 转换为实际值
    for (int i = 0; i < 3; i++) {
        handle->data.acc[i] = handle->data.acc_raw[i] * JY901S_ACC_SCALE;
    }
    
    return true;
}

/**
 * @brief 读取角速度数据
 */
bool JY901S_ReadGyroscope(JY901S_Handle *handle)
{
    if (!handle || !handle->initialized) {
        return false;
    }
    
    uint8_t data[6];
    if (!JY901S_ReadRegisters(handle, JY901S_REG_GYRO, data, 6)) {
        return false;
    }
    
    // 组合16位数据 (小端序)
    handle->data.gyro_raw[0] = (int16_t)((data[1] << 8) | data[0]);  // X轴
    handle->data.gyro_raw[1] = (int16_t)((data[3] << 8) | data[2]);  // Y轴
    handle->data.gyro_raw[2] = (int16_t)((data[5] << 8) | data[4]);  // Z轴
    
    // 转换为实际值
    for (int i = 0; i < 3; i++) {
        handle->data.gyro[i] = handle->data.gyro_raw[i] * JY901S_GYRO_SCALE;
    }
    
    return true;
}

/**
 * @brief 读取角度数据
 */
bool JY901S_ReadAngle(JY901S_Handle *handle)
{
    if (!handle || !handle->initialized) {
        return false;
    }
    
    uint8_t data[6];
    if (!JY901S_ReadRegisters(handle, JY901S_REG_ANGLE, data, 6)) {
        return false;
    }
    
    // 组合16位数据 (小端序)
    handle->data.angle_raw[0] = (int16_t)((data[1] << 8) | data[0]);  // Roll
    handle->data.angle_raw[1] = (int16_t)((data[3] << 8) | data[2]);  // Pitch
    handle->data.angle_raw[2] = (int16_t)((data[5] << 8) | data[4]);  // Yaw
    
    // 转换为实际值
    for (int i = 0; i < 3; i++) {
        handle->data.angle[i] = handle->data.angle_raw[i] * JY901S_ANGLE_SCALE;
    }
    
    return true;
}

/**
 * @brief 读取磁场数据
 */
bool JY901S_ReadMagnetometer(JY901S_Handle *handle)
{
    if (!handle || !handle->initialized) {
        return false;
    }
    
    uint8_t data[6];
    if (!JY901S_ReadRegisters(handle, JY901S_REG_MAG, data, 6)) {
        return false;
    }
    
    // 组合16位数据 (小端序)
    handle->data.mag_raw[0] = (int16_t)((data[1] << 8) | data[0]);  // X轴
    handle->data.mag_raw[1] = (int16_t)((data[3] << 8) | data[2]);  // Y轴
    handle->data.mag_raw[2] = (int16_t)((data[5] << 8) | data[4]);  // Z轴
    
    // 转换为实际值
    for (int i = 0; i < 3; i++) {
        handle->data.mag[i] = handle->data.mag_raw[i] * JY901S_MAG_SCALE;
    }
    
    return true;
}

/**
 * @brief 读取温度数据
 */
bool JY901S_ReadTemperature(JY901S_Handle *handle)
{
    if (!handle || !handle->initialized) {
        return false;
    }
    
    uint8_t data[2];
    if (!JY901S_ReadRegisters(handle, JY901S_REG_TEMP, data, 2)) {
        return false;
    }
    
    // 组合16位数据 (小端序)
    handle->data.temp_raw = (int16_t)((data[1] << 8) | data[0]);
    
    // 转换为实际值
    handle->data.temperature = handle->data.temp_raw * JY901S_TEMP_SCALE;
    
    return true;
}

/**
 * @brief 校准传感器
 */
bool JY901S_Calibrate(JY901S_Handle *handle)
{
    if (!handle || !handle->initialized) {
        return false;
    }
    
    // 发送校准命令
    if (!JY901S_WriteRegister(handle, JY901S_REG_CALSW, 0x01)) {
        return false;
    }
    
    // 等待校准完成
    handle->delay(3000);  // 校准需要约3秒
    
    return true;
}

/**
 * @brief 复位传感器
 */
bool JY901S_Reset(JY901S_Handle *handle)
{
    if (!handle || !handle->initialized) {
        return false;
    }
    
    // 发送复位命令
    if (!JY901S_WriteRegister(handle, JY901S_REG_CALSW, 0x00)) {
        return false;
    }
    
    // 等待复位完成
    handle->delay(100);
    
    return true;
}

/**
 * @brief 设置数据输出频率
 */
bool JY901S_SetOutputRate(JY901S_Handle *handle, uint8_t rate)
{
    if (!handle || !handle->initialized) {
        return false;
    }

    return JY901S_WriteRegister(handle, JY901S_REG_RRATE, rate);
}

/**
 * @brief 获取Roll角度 (°)
 */
float JY901S_GetRoll(JY901S_Handle *handle)
{
    if (!handle || !handle->initialized) {
        return 0.0f;
    }
    return handle->data.angle[0];
}

/**
 * @brief 获取Pitch角度 (°)
 */
float JY901S_GetPitch(JY901S_Handle *handle)
{
    if (!handle || !handle->initialized) {
        return 0.0f;
    }
    return handle->data.angle[1];
}

/**
 * @brief 获取Yaw角度 (°)
 */
float JY901S_GetYaw(JY901S_Handle *handle)
{
    if (!handle || !handle->initialized) {
        return 0.0f;
    }
    return handle->data.angle[2];
}

// --- 内部辅助函数实现 ---

/**
 * @brief 转换原始数据
 */
static void JY901S_ConvertRawData(JY901S_Handle *handle)
{
    // 加速度转换
    for (int i = 0; i < 3; i++) {
        handle->data.acc[i] = handle->data.acc_raw[i] * JY901S_ACC_SCALE;
    }

    // 角速度转换
    for (int i = 0; i < 3; i++) {
        handle->data.gyro[i] = handle->data.gyro_raw[i] * JY901S_GYRO_SCALE;
    }

    // 角度转换
    for (int i = 0; i < 3; i++) {
        handle->data.angle[i] = handle->data.angle_raw[i] * JY901S_ANGLE_SCALE;
    }

    // 磁场转换
    for (int i = 0; i < 3; i++) {
        handle->data.mag[i] = handle->data.mag_raw[i] * JY901S_MAG_SCALE;
    }

    // 温度转换
    handle->data.temperature = handle->data.temp_raw * JY901S_TEMP_SCALE;
}

/**
 * @brief 写寄存器
 */
static bool JY901S_WriteRegister(JY901S_Handle *handle, uint8_t reg, uint8_t value)
{
    return handle->i2c_write(handle->config.i2c_addr, reg, &value, 1);
}

/**
 * @brief 读寄存器
 */
static bool JY901S_ReadRegisters(JY901S_Handle *handle, uint8_t reg, uint8_t *data, uint16_t len)
{
    return handle->i2c_read(handle->config.i2c_addr, reg, data, len);
}
