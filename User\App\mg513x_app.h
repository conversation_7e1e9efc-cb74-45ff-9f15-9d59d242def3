#ifndef __MG513X_APP_H__
#define __MG513X_APP_H__

#include "MyDefine.h"
#include "mg513x_driver.h"

// 电机实例声明
extern MG513X_Motor left_mg513x;
extern MG513X_Motor right_mg513x;

/**
 * @brief 初始化MG513X电机应用
 */
void MG513X_App_Init(void);

/**
 * @brief MG513X电机应用任务 (需要周期性调用)
 */
void MG513X_App_Task(void);

/**
 * @brief 设置左电机速度
 * @param speed 速度值 (-999 ~ 999)
 */
void MG513X_App_SetLeftSpeed(int speed);

/**
 * @brief 设置右电机速度
 * @param speed 速度值 (-999 ~ 999)
 */
void MG513X_App_SetRightSpeed(int speed);

/**
 * @brief 设置双电机速度
 * @param left_speed 左电机速度
 * @param right_speed 右电机速度
 */
void MG513X_App_SetSpeed(int left_speed, int right_speed);

/**
 * @brief 获取左电机速度 (cm/s)
 * @return 速度值
 */
float MG513X_App_GetLeftSpeedCmS(void);

/**
 * @brief 获取右电机速度 (cm/s)
 * @return 速度值
 */
float MG513X_App_GetRightSpeedCmS(void);

/**
 * @brief 停止所有电机
 */
void MG513X_App_StopAll(void);

/**
 * @brief 刹车所有电机
 */
void MG513X_App_BrakeAll(void);

/**
 * @brief 重置所有编码器
 */
void MG513X_App_ResetEncoders(void);

/**
 * @brief 获取左电机编码器总计数
 * @return 总计数值
 */
int32_t MG513X_App_GetLeftTotalCount(void);

/**
 * @brief 获取右电机编码器总计数
 * @return 总计数值
 */
int32_t MG513X_App_GetRightTotalCount(void);

#endif /* __MG513X_APP_H__ */
