# JY901S陀螺仪I2C驱动库

## 📖 简介

这是一个高度模块化、便于移植的JY901S陀螺仪I2C驱动库。该库采用分层设计，核心驱动与平台无关，只需要实现4个简单的移植接口函数即可在任何平台使用。

## ✨ 特性

- 🔧 **高度模块化**: 核心驱动与平台无关
- 🚀 **便于移植**: 只需实现4个接口函数
- 📊 **功能完整**: 支持加速度、角速度、角度、温度读取
- 🛠️ **易于使用**: 提供丰富的API和示例代码
- 🔒 **稳定可靠**: 包含错误处理和状态监控
- 📱 **STM32优化**: 提供STM32平台专用适配层

## 📁 文件结构

```
JY901S_Library/
├── jy901s.h              # 核心驱动头文件 (平台无关)
├── jy901s.c              # 核心驱动实现 (平台无关)
├── jy901s_stm32.h        # STM32平台适配层头文件
├── jy901s_stm32.c        # STM32平台适配层实现
├── jy901s_example.c      # 使用示例代码
└── README.md             # 本说明文档
```

## 🔌 硬件连接

### JY901S引脚定义
```
JY901S    功能      STM32F407
VCC   →   电源   →   3.3V/5V
GND   →   地线   →   GND
SDA   →   数据   →   PB7 (I2C1_SDA)
SCL   →   时钟   →   PB6 (I2C1_SCL)
```

### STM32CubeMX配置
1. 配置I2C1为标准模式
2. 时钟频率设置为100kHz
3. 确保I2C引脚配置正确

## 🚀 快速开始

### 1. 添加文件到项目
将以下文件添加到您的STM32项目中：
- `jy901s.h` 和 `jy901s.c`
- `jy901s_stm32.h` 和 `jy901s_stm32.c`

### 2. 包含头文件
```c
#include "jy901s_stm32.h"
```

### 3. 初始化JY901S
```c
// 创建句柄
JY901S_STM32_Handle_t jy901s_handle;

// 配置参数
JY901S_STM32_Config_t config = JY901S_STM32_STANDARD_CONFIG(&hi2c1);

// 初始化
if (JY901S_STM32_Init(&jy901s_handle, &config)) {
    printf("JY901S initialized successfully!\r\n");
} else {
    printf("JY901S initialization failed!\r\n");
}
```

### 4. 读取传感器数据
```c
// 读取所有数据
if (JY901S_STM32_ReadAllData(&jy901s_handle)) {
    // 获取角度数据
    float roll = JY901S_STM32_GetRoll(&jy901s_handle);
    float pitch = JY901S_STM32_GetPitch(&jy901s_handle);
    float yaw = JY901S_STM32_GetYaw(&jy901s_handle);
    
    // 获取加速度数据
    float acc_x, acc_y, acc_z;
    JY901S_STM32_GetAcceleration(&jy901s_handle, &acc_x, &acc_y, &acc_z);
    
    // 获取角速度数据
    float gyro_x, gyro_y, gyro_z;
    JY901S_STM32_GetGyroscope(&jy901s_handle, &gyro_x, &gyro_y, &gyro_z);
    
    // 获取温度数据
    float temperature = JY901S_STM32_GetTemperature(&jy901s_handle);
    
    printf("Yaw: %.1f°, Roll: %.1f°, Pitch: %.1f°\r\n", yaw, roll, pitch);
}
```

## 📚 API参考

### 初始化函数
```c
bool JY901S_STM32_Init(JY901S_STM32_Handle_t *handle, JY901S_STM32_Config_t *config);
```

### 数据读取函数
```c
bool JY901S_STM32_ReadAllData(JY901S_STM32_Handle_t *handle);
```

### 角度获取函数
```c
float JY901S_STM32_GetRoll(JY901S_STM32_Handle_t *handle);    // 横滚角
float JY901S_STM32_GetPitch(JY901S_STM32_Handle_t *handle);   // 俯仰角
float JY901S_STM32_GetYaw(JY901S_STM32_Handle_t *handle);     // 偏航角
```

### 加速度获取函数
```c
bool JY901S_STM32_GetAcceleration(JY901S_STM32_Handle_t *handle, 
                                  float *acc_x, float *acc_y, float *acc_z);
```

### 角速度获取函数
```c
bool JY901S_STM32_GetGyroscope(JY901S_STM32_Handle_t *handle, 
                               float *gyro_x, float *gyro_y, float *gyro_z);
```

### 其他函数
```c
float JY901S_STM32_GetTemperature(JY901S_STM32_Handle_t *handle);  // 温度
bool JY901S_STM32_Calibrate(JY901S_STM32_Handle_t *handle);        // 校准
bool JY901S_STM32_Reset(JY901S_STM32_Handle_t *handle);            // 复位
bool JY901S_STM32_IsDataReady(JY901S_STM32_Handle_t *handle);      // 数据就绪
uint32_t JY901S_STM32_GetErrorCount(JY901S_STM32_Handle_t *handle); // 错误计数
```

## 🔧 配置选项

### 标准配置 (推荐)
```c
JY901S_STM32_Config_t config = JY901S_STM32_STANDARD_CONFIG(&hi2c1);
```

### 自定义配置
```c
JY901S_STM32_Config_t config = {
    .hi2c = &hi2c1,                    // I2C句柄
    .device_addr = JY901S_I2C_ADDR,    // 设备地址 (0x50)
    .timeout_ms = 1000,                // 超时时间 (毫秒)
    .auto_calibration = false          // 是否自动校准
};
```

## 🔄 移植到其他平台

如果要移植到非STM32平台，只需要实现以下4个接口函数：

### 1. I2C写函数
```c
bool Your_I2C_Write(uint8_t device_addr, uint8_t reg_addr, 
                    uint8_t *data, uint16_t len, uint16_t timeout_ms);
```

### 2. I2C读函数
```c
bool Your_I2C_Read(uint8_t device_addr, uint8_t reg_addr, 
                   uint8_t *data, uint16_t len, uint16_t timeout_ms);
```

### 3. 延时函数
```c
void Your_Delay(uint32_t ms);
```

### 4. 时间戳函数
```c
uint32_t Your_GetTick(void);
```

然后使用核心驱动API：
```c
JY901S_Handle_t handle;
JY901S_Config_t config = { /* 配置参数 */ };

JY901S_Init(&handle, &config, 
            Your_I2C_Write, Your_I2C_Read, 
            Your_Delay, Your_GetTick);
```

## 🐛 故障排除

### 初始化失败
- 检查I2C硬件连接
- 检查设备地址是否正确 (0x50)
- 检查I2C时钟配置
- 检查电源供应是否稳定

### 数据读取失败
- 检查I2C通信是否正常
- 降低I2C时钟频率
- 检查传感器供电
- 查看错误计数: `JY901S_STM32_GetErrorCount()`

### 数据异常
- 检查传感器安装方向
- 尝试重新校准: `JY901S_STM32_Calibrate()`
- 检查环境磁场干扰
- 检查温度是否在正常范围

## 📊 技术规格

| 参数 | 规格 |
|------|------|
| 通信接口 | I2C |
| 设备地址 | 0x50 (7位地址) |
| 供电电压 | 3.3V / 5V |
| 角度量程 | ±180° |
| 加速度量程 | ±16g |
| 角速度量程 | ±2000°/s |
| 温度量程 | -40°C ~ +85°C |
| 数据更新率 | 最高200Hz |

## 📝 示例代码

详细的使用示例请参考 `jy901s_example.c` 文件，包含：
- 基础初始化和数据读取
- PID控制应用
- 状态监控
- 校准和复位
- 完整测试程序

## 📄 许可证

本库采用MIT许可证，可自由使用和修改。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个库！

## 📞 技术支持

如果您在使用过程中遇到问题，请：
1. 检查硬件连接
2. 查看错误计数和状态
3. 参考故障排除章节
4. 查看示例代码

---

**祝您使用愉快！** 🎉
