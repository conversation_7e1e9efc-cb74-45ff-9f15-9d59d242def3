#ifndef __JY901S_STM32_PORT_H__
#define __JY901S_STM32_PORT_H__

#include "jy901s_driver.h"

// 根据您的项目包含相应的HAL库头文件
#include "MyDefine.h"  // 这里包含了您项目的所有头文件

/**
 * @brief STM32平台JY901S配置结构体
 */
typedef struct {
    I2C_HandleTypeDef *hi2c;        // I2C句柄
    uint32_t timeout_ms;            // I2C超时时间 (ms)
    uint8_t device_addr;            // 设备地址 (7位地址)
} JY901S_STM32_Config;

/**
 * @brief STM32平台JY901S句柄
 */
typedef struct {
    JY901S_Handle jy901s;           // JY901S通用句柄
    JY901S_STM32_Config stm32_config; // STM32特定配置
} JY901S_STM32_Handle;

// --- STM32平台接口函数 ---

/**
 * @brief STM32平台I2C写函数
 * @param addr I2C设备地址 (7位地址)
 * @param reg 寄存器地址
 * @param data 要写入的数据
 * @param len 数据长度
 * @return true-成功, false-失败
 */
bool JY901S_STM32_I2C_Write(uint8_t addr, uint8_t reg, uint8_t *data, uint16_t len);

/**
 * @brief STM32平台I2C读函数
 * @param addr I2C设备地址 (7位地址)
 * @param reg 寄存器地址
 * @param data 读取数据缓冲区
 * @param len 数据长度
 * @return true-成功, false-失败
 */
bool JY901S_STM32_I2C_Read(uint8_t addr, uint8_t reg, uint8_t *data, uint16_t len);

/**
 * @brief STM32平台延时函数
 * @param ms 延时毫秒数
 */
void JY901S_STM32_Delay(uint32_t ms);

/**
 * @brief STM32平台获取系统时间函数
 * @return 系统时间戳 (ms)
 */
uint32_t JY901S_STM32_GetTick(void);

// --- 高级封装函数 ---

/**
 * @brief 初始化STM32平台的JY901S
 * @param handle STM32句柄
 * @param hi2c I2C句柄
 * @param device_addr 设备地址 (默认0x50)
 * @param timeout_ms I2C超时时间 (默认1000ms)
 * @param auto_calibration 是否自动校准
 * @return true-成功, false-失败
 */
bool JY901S_STM32_Init(JY901S_STM32_Handle *handle, 
                       I2C_HandleTypeDef *hi2c,
                       uint8_t device_addr,
                       uint32_t timeout_ms,
                       bool auto_calibration);

/**
 * @brief 读取所有传感器数据 (STM32封装)
 * @param handle STM32句柄
 * @return true-成功, false-失败
 */
bool JY901S_STM32_ReadAllData(JY901S_STM32_Handle *handle);

/**
 * @brief 获取Roll角度 (STM32封装)
 * @param handle STM32句柄
 * @return Roll角度 (°)
 */
float JY901S_STM32_GetRoll(JY901S_STM32_Handle *handle);

/**
 * @brief 获取Pitch角度 (STM32封装)
 * @param handle STM32句柄
 * @return Pitch角度 (°)
 */
float JY901S_STM32_GetPitch(JY901S_STM32_Handle *handle);

/**
 * @brief 获取Yaw角度 (STM32封装)
 * @param handle STM32句柄
 * @return Yaw角度 (°)
 */
float JY901S_STM32_GetYaw(JY901S_STM32_Handle *handle);

/**
 * @brief 获取加速度数据 (STM32封装)
 * @param handle STM32句柄
 * @param acc_x X轴加速度 (g)
 * @param acc_y Y轴加速度 (g)
 * @param acc_z Z轴加速度 (g)
 * @return true-成功, false-失败
 */
bool JY901S_STM32_GetAcceleration(JY901S_STM32_Handle *handle, float *acc_x, float *acc_y, float *acc_z);

/**
 * @brief 获取角速度数据 (STM32封装)
 * @param handle STM32句柄
 * @param gyro_x X轴角速度 (°/s)
 * @param gyro_y Y轴角速度 (°/s)
 * @param gyro_z Z轴角速度 (°/s)
 * @return true-成功, false-失败
 */
bool JY901S_STM32_GetGyroscope(JY901S_STM32_Handle *handle, float *gyro_x, float *gyro_y, float *gyro_z);

/**
 * @brief 获取温度数据 (STM32封装)
 * @param handle STM32句柄
 * @return 温度 (°C)
 */
float JY901S_STM32_GetTemperature(JY901S_STM32_Handle *handle);

/**
 * @brief 校准传感器 (STM32封装)
 * @param handle STM32句柄
 * @return true-成功, false-失败
 */
bool JY901S_STM32_Calibrate(JY901S_STM32_Handle *handle);

/**
 * @brief 复位传感器 (STM32封装)
 * @param handle STM32句柄
 * @return true-成功, false-失败
 */
bool JY901S_STM32_Reset(JY901S_STM32_Handle *handle);

// --- 全局变量声明 (用于移植接口) ---
extern JY901S_STM32_Handle *g_jy901s_stm32_handle;

#endif /* __JY901S_STM32_PORT_H__ */
