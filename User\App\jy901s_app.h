#ifndef __JY901S_APP_H__
#define __JY901S_APP_H__

#include "MyDefine.h"
#include "jy901s_stm32_port.h"

// JY901S应用层实例声明
extern JY901S_STM32_Handle jy901s_handle;

/**
 * @brief 初始化JY901S应用
 * @return true-成功, false-失败
 */
bool JY901S_App_Init(void);

/**
 * @brief JY901S应用任务 (需要周期性调用)
 * @return true-成功, false-失败
 */
bool JY901S_App_Task(void);

/**
 * @brief 获取Roll角度 (横滚角)
 * @return Roll角度 (°)
 */
float JY901S_App_GetRoll(void);

/**
 * @brief 获取Pitch角度 (俯仰角)
 * @return Pitch角度 (°)
 */
float JY901S_App_GetPitch(void);

/**
 * @brief 获取Yaw角度 (偏航角)
 * @return Yaw角度 (°)
 */
float JY901S_App_GetYaw(void);

/**
 * @brief 获取X轴加速度
 * @return X轴加速度 (g)
 */
float JY901S_App_GetAccX(void);

/**
 * @brief 获取Y轴加速度
 * @return Y轴加速度 (g)
 */
float JY901S_App_GetAccY(void);

/**
 * @brief 获取Z轴加速度
 * @return Z轴加速度 (g)
 */
float JY901S_App_GetAccZ(void);

/**
 * @brief 获取X轴角速度
 * @return X轴角速度 (°/s)
 */
float JY901S_App_GetGyroX(void);

/**
 * @brief 获取Y轴角速度
 * @return Y轴角速度 (°/s)
 */
float JY901S_App_GetGyroY(void);

/**
 * @brief 获取Z轴角速度
 * @return Z轴角速度 (°/s)
 */
float JY901S_App_GetGyroZ(void);

/**
 * @brief 获取温度
 * @return 温度 (°C)
 */
float JY901S_App_GetTemperature(void);

/**
 * @brief 校准传感器
 * @return true-成功, false-失败
 */
bool JY901S_App_Calibrate(void);

/**
 * @brief 复位传感器
 * @return true-成功, false-失败
 */
bool JY901S_App_Reset(void);

/**
 * @brief 检查数据是否就绪
 * @return true-数据就绪, false-数据未就绪
 */
bool JY901S_App_IsDataReady(void);

/**
 * @brief 获取上次更新时间
 * @return 时间戳 (ms)
 */
uint32_t JY901S_App_GetLastUpdateTime(void);

/**
 * @brief 打印传感器数据 (调试用)
 */
void JY901S_App_PrintData(void);

#endif /* __JY901S_APP_H__ */
