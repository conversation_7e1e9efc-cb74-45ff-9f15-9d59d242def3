#ifndef __MG513X_ENCODER_DRIVER_H__
#define __MG513X_ENCODER_DRIVER_H__

#include "MyDefine.h"

// --- MG513X编码器参数配置 ---
// MG513X编码器每转一圈的脉冲数 (PPR)
#define MG513X_ENCODER_PPR (11 * 30 * 4)  // 11线/相, 30倍减速比, 4倍频 = 1320
// 车轮直径 (单位: 厘米) - 可根据实际情况修改
#define MG513X_WHEEL_DIAMETER_CM 6.5f
// 采样时间 (秒) - 与调度器任务周期一致
#define MG513X_SAMPLING_TIME_S 0.005f     // 5ms

// 自动计算参数
#define PI 3.14159265f
#define MG513X_WHEEL_CIRCUMFERENCE_CM (MG513X_WHEEL_DIAMETER_CM * PI)

/**
 * @brief MG513X编码器数据结构体
 */
typedef struct
{
    TIM_HandleTypeDef *htim;        // 编码器定时器
    uint8_t reverse;                // 编码器方向是否反转。0-正常，1-反转
    int16_t count;                  // 当前采样周期内的原始计数值
    int32_t total_count;            // 累计总计数值
    float speed_cm_s;               // 计算出的线速度 (cm/s)
    float speed_rpm;                // 计算出的转速 (RPM)
    float distance_cm;              // 累计行驶距离 (cm)
} MG513X_Encoder;

/**
 * @brief 初始化MG513X编码器驱动
 * @param encoder 编码器结构体指针
 * @param htim 编码器定时器
 * @param reverse 编码器方向是否反转。0-正常，1-反转
 */
void MG513X_Encoder_Init(MG513X_Encoder* encoder, TIM_HandleTypeDef *htim, uint8_t reverse);

/**
 * @brief 更新MG513X编码器数据 (应周期性调用, 例如5ms一次)
 * @param encoder 编码器结构体指针
 */
void MG513X_Encoder_Update(MG513X_Encoder* encoder);

/**
 * @brief 获取编码器线速度 (cm/s)
 * @param encoder 编码器结构体指针
 * @return 线速度值 (cm/s)
 */
float MG513X_Encoder_GetSpeedCmS(MG513X_Encoder* encoder);

/**
 * @brief 获取编码器转速 (RPM)
 * @param encoder 编码器结构体指针
 * @return 转速值 (RPM)
 */
float MG513X_Encoder_GetSpeedRPM(MG513X_Encoder* encoder);

/**
 * @brief 获取编码器总计数
 * @param encoder 编码器结构体指针
 * @return 总计数值
 */
int32_t MG513X_Encoder_GetTotalCount(MG513X_Encoder* encoder);

/**
 * @brief 获取累计行驶距离 (cm)
 * @param encoder 编码器结构体指针
 * @return 距离值 (cm)
 */
float MG513X_Encoder_GetDistanceCm(MG513X_Encoder* encoder);

/**
 * @brief 重置编码器计数和距离
 * @param encoder 编码器结构体指针
 */
void MG513X_Encoder_Reset(MG513X_Encoder* encoder);

/**
 * @brief 设置车轮直径 (运行时修改)
 * @param diameter_cm 车轮直径 (cm)
 */
void MG513X_Encoder_SetWheelDiameter(float diameter_cm);

/**
 * @brief 获取当前车轮直径设置
 * @return 车轮直径 (cm)
 */
float MG513X_Encoder_GetWheelDiameter(void);

#endif /* __MG513X_ENCODER_DRIVER_H__ */
