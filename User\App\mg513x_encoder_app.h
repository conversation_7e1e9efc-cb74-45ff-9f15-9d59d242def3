#ifndef __MG513X_ENCODER_APP_H__
#define __MG513X_ENCODER_APP_H__

#include "MyDefine.h"
#include "mg513x_encoder_driver.h"

// 编码器实例声明
extern MG513X_Encoder left_mg513x_encoder;
extern MG513X_Encoder right_mg513x_encoder;

/**
 * @brief 初始化MG513X编码器应用
 */
void MG513X_Encoder_App_Init(void);

/**
 * @brief MG513X编码器应用任务 (需要周期性调用)
 */
void MG513X_Encoder_App_Task(void);

/**
 * @brief 获取左编码器速度 (cm/s)
 * @return 速度值
 */
float MG513X_Encoder_App_GetLeftSpeedCmS(void);

/**
 * @brief 获取右编码器速度 (cm/s)
 * @return 速度值
 */
float MG513X_Encoder_App_GetRightSpeedCmS(void);

/**
 * @brief 获取左编码器转速 (RPM)
 * @return 转速值
 */
float MG513X_Encoder_App_GetLeftSpeedRPM(void);

/**
 * @brief 获取右编码器转速 (RPM)
 * @return 转速值
 */
float MG513X_Encoder_App_GetRightSpeedRPM(void);

/**
 * @brief 获取左编码器总计数
 * @return 总计数值
 */
int32_t MG513X_Encoder_App_GetLeftTotalCount(void);

/**
 * @brief 获取右编码器总计数
 * @return 总计数值
 */
int32_t MG513X_Encoder_App_GetRightTotalCount(void);

/**
 * @brief 获取左编码器累计距离 (cm)
 * @return 距离值
 */
float MG513X_Encoder_App_GetLeftDistanceCm(void);

/**
 * @brief 获取右编码器累计距离 (cm)
 * @return 距离值
 */
float MG513X_Encoder_App_GetRightDistanceCm(void);

/**
 * @brief 获取小车平均速度 (cm/s)
 * @return 平均速度值
 */
float MG513X_Encoder_App_GetAverageSpeedCmS(void);

/**
 * @brief 获取小车平均行驶距离 (cm)
 * @return 平均距离值
 */
float MG513X_Encoder_App_GetAverageDistanceCm(void);

/**
 * @brief 重置所有编码器
 */
void MG513X_Encoder_App_ResetAll(void);

/**
 * @brief 设置车轮直径
 * @param diameter_cm 车轮直径 (cm)
 */
void MG513X_Encoder_App_SetWheelDiameter(float diameter_cm);

#endif /* __MG513X_ENCODER_APP_H__ */
