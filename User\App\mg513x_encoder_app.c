#include "mg513x_encoder_app.h"

// MG513X编码器实例
MG513X_Encoder left_mg513x_encoder;
MG513X_Encoder right_mg513x_encoder;

/**
 * @brief 初始化MG513X编码器应用
 */
void MG513X_Encoder_App_Init(void)
{
    // 初始化左编码器
    // 注意：这里的定时器配置需要根据您的实际硬件连接进行修改
    MG513X_Encoder_Init(&left_mg513x_encoder, &htim3, 0);  // 左编码器，方向不反转
    
    // 初始化右编码器
    MG513X_Encoder_Init(&right_mg513x_encoder, &htim4, 1); // 右编码器，方向反转
    
    // 打印初始化信息
    Uart_Printf(&huart1, "=== MG513X Encoders Initialized ===\r\n");
    Uart_Printf(&huart1, "Encoder PPR: %d\r\n", MG513X_ENCODER_PPR);
    Uart_Printf(&huart1, "Wheel Diameter: %.1f cm\r\n", MG513X_Encoder_GetWheelDiameter());
    Uart_Printf(&huart1, "Wheel Circumference: %.2f cm\r\n", MG513X_WHEEL_CIRCUMFERENCE_CM);
    Uart_Printf(&huart1, "Sampling Time: %.3f s\r\n", MG513X_SAMPLING_TIME_S);
}

/**
 * @brief MG513X编码器应用任务 (需要周期性调用)
 */
void MG513X_Encoder_App_Task(void)
{
    // 更新编码器数据
    MG513X_Encoder_Update(&left_mg513x_encoder);
    MG513X_Encoder_Update(&right_mg513x_encoder);
    
    // 可选：打印调试信息 (取消注释以启用)
    /*
    static uint16_t debug_counter = 0;
    if (++debug_counter >= 200) { // 每1秒打印一次 (5ms * 200 = 1s)
        debug_counter = 0;
        Uart_Printf(&huart1, "L:%.1fcm/s(%.0fRPM) R:%.1fcm/s(%.0fRPM) Dist:%.1fcm\r\n",
                   left_mg513x_encoder.speed_cm_s, left_mg513x_encoder.speed_rpm,
                   right_mg513x_encoder.speed_cm_s, right_mg513x_encoder.speed_rpm,
                   MG513X_Encoder_App_GetAverageDistanceCm());
    }
    */
}

/**
 * @brief 获取左编码器速度 (cm/s)
 */
float MG513X_Encoder_App_GetLeftSpeedCmS(void)
{
    return MG513X_Encoder_GetSpeedCmS(&left_mg513x_encoder);
}

/**
 * @brief 获取右编码器速度 (cm/s)
 */
float MG513X_Encoder_App_GetRightSpeedCmS(void)
{
    return MG513X_Encoder_GetSpeedCmS(&right_mg513x_encoder);
}

/**
 * @brief 获取左编码器转速 (RPM)
 */
float MG513X_Encoder_App_GetLeftSpeedRPM(void)
{
    return MG513X_Encoder_GetSpeedRPM(&left_mg513x_encoder);
}

/**
 * @brief 获取右编码器转速 (RPM)
 */
float MG513X_Encoder_App_GetRightSpeedRPM(void)
{
    return MG513X_Encoder_GetSpeedRPM(&right_mg513x_encoder);
}

/**
 * @brief 获取左编码器总计数
 */
int32_t MG513X_Encoder_App_GetLeftTotalCount(void)
{
    return MG513X_Encoder_GetTotalCount(&left_mg513x_encoder);
}

/**
 * @brief 获取右编码器总计数
 */
int32_t MG513X_Encoder_App_GetRightTotalCount(void)
{
    return MG513X_Encoder_GetTotalCount(&right_mg513x_encoder);
}

/**
 * @brief 获取左编码器累计距离 (cm)
 */
float MG513X_Encoder_App_GetLeftDistanceCm(void)
{
    return MG513X_Encoder_GetDistanceCm(&left_mg513x_encoder);
}

/**
 * @brief 获取右编码器累计距离 (cm)
 */
float MG513X_Encoder_App_GetRightDistanceCm(void)
{
    return MG513X_Encoder_GetDistanceCm(&right_mg513x_encoder);
}

/**
 * @brief 获取小车平均速度 (cm/s)
 */
float MG513X_Encoder_App_GetAverageSpeedCmS(void)
{
    return (left_mg513x_encoder.speed_cm_s + right_mg513x_encoder.speed_cm_s) / 2.0f;
}

/**
 * @brief 获取小车平均行驶距离 (cm)
 */
float MG513X_Encoder_App_GetAverageDistanceCm(void)
{
    return (left_mg513x_encoder.distance_cm + right_mg513x_encoder.distance_cm) / 2.0f;
}

/**
 * @brief 重置所有编码器
 */
void MG513X_Encoder_App_ResetAll(void)
{
    MG513X_Encoder_Reset(&left_mg513x_encoder);
    MG513X_Encoder_Reset(&right_mg513x_encoder);
    Uart_Printf(&huart1, "MG513X Encoders Reset\r\n");
}

/**
 * @brief 设置车轮直径
 */
void MG513X_Encoder_App_SetWheelDiameter(float diameter_cm)
{
    MG513X_Encoder_SetWheelDiameter(diameter_cm);
    Uart_Printf(&huart1, "Wheel diameter set to: %.1f cm\r\n", diameter_cm);
}
