/**
 * @file jy901s_stm32.c
 * @brief JY901S陀螺仪STM32平台移植适配层实现
 * @version 1.0
 * @date 2024-07-28
 * <AUTHOR> Agent
 */

#include "jy901s_stm32.h"

// 全局变量用于移植接口
I2C_HandleTypeDef *g_jy901s_hi2c = NULL;

// --- STM32平台移植接口函数实现 ---

/**
 * @brief STM32平台I2C写函数
 */
bool JY901S_STM32_I2C_Write(uint8_t device_addr, uint8_t reg_addr, 
                            uint8_t *data, uint16_t len, uint16_t timeout_ms)
{
    if (!g_jy901s_hi2c) {
        return false;
    }
    
    HAL_StatusTypeDef status;
    
    // 使用HAL_I2C_Mem_Write进行寄存器写入
    status = HAL_I2C_Mem_Write(g_jy901s_hi2c,
                               device_addr << 1,  // 转换为8位地址
                               reg_addr,
                               I2C_MEMADD_SIZE_8BIT,
                               data,
                               len,
                               timeout_ms);
    
    return (status == HAL_OK);
}

/**
 * @brief STM32平台I2C读函数
 */
bool JY901S_STM32_I2C_Read(uint8_t device_addr, uint8_t reg_addr, 
                           uint8_t *data, uint16_t len, uint16_t timeout_ms)
{
    if (!g_jy901s_hi2c) {
        return false;
    }
    
    HAL_StatusTypeDef status;
    
    // 使用HAL_I2C_Mem_Read进行寄存器读取
    status = HAL_I2C_Mem_Read(g_jy901s_hi2c,
                              device_addr << 1,  // 转换为8位地址
                              reg_addr,
                              I2C_MEMADD_SIZE_8BIT,
                              data,
                              len,
                              timeout_ms);
    
    return (status == HAL_OK);
}

/**
 * @brief STM32平台延时函数
 */
void JY901S_STM32_Delay(uint32_t ms)
{
    HAL_Delay(ms);
}

/**
 * @brief STM32平台获取系统时间戳函数
 */
uint32_t JY901S_STM32_GetTick(void)
{
    return HAL_GetTick();
}

// --- STM32平台高级封装函数实现 ---

/**
 * @brief 初始化STM32平台的JY901S
 */
bool JY901S_STM32_Init(JY901S_STM32_Handle_t *handle, JY901S_STM32_Config_t *config)
{
    if (!handle || !config || !config->hi2c) {
        return false;
    }
    
    // 保存I2C句柄
    handle->hi2c = config->hi2c;
    g_jy901s_hi2c = config->hi2c;
    
    // 配置JY901S通用参数
    JY901S_Config_t jy901s_config = {
        .device_addr = config->device_addr,
        .timeout_ms = config->timeout_ms,
        .auto_calibration = config->auto_calibration,
        .output_rate = 10  // 默认10Hz输出频率
    };
    
    // 初始化JY901S驱动
    return JY901S_Init(&handle->jy901s,
                       &jy901s_config,
                       JY901S_STM32_I2C_Write,
                       JY901S_STM32_I2C_Read,
                       JY901S_STM32_Delay,
                       JY901S_STM32_GetTick);
}

/**
 * @brief 读取所有传感器数据 (STM32封装)
 */
bool JY901S_STM32_ReadAllData(JY901S_STM32_Handle_t *handle)
{
    if (!handle) {
        return false;
    }
    
    return JY901S_ReadAllData(&handle->jy901s);
}

/**
 * @brief 获取Roll角度 (STM32封装)
 */
float JY901S_STM32_GetRoll(JY901S_STM32_Handle_t *handle)
{
    if (!handle) {
        return 0.0f;
    }
    
    return JY901S_GetRoll(&handle->jy901s);
}

/**
 * @brief 获取Pitch角度 (STM32封装)
 */
float JY901S_STM32_GetPitch(JY901S_STM32_Handle_t *handle)
{
    if (!handle) {
        return 0.0f;
    }
    
    return JY901S_GetPitch(&handle->jy901s);
}

/**
 * @brief 获取Yaw角度 (STM32封装)
 */
float JY901S_STM32_GetYaw(JY901S_STM32_Handle_t *handle)
{
    if (!handle) {
        return 0.0f;
    }
    
    return JY901S_GetYaw(&handle->jy901s);
}

/**
 * @brief 获取加速度数据 (STM32封装)
 */
bool JY901S_STM32_GetAcceleration(JY901S_STM32_Handle_t *handle, 
                                  float *acc_x, float *acc_y, float *acc_z)
{
    if (!handle || !acc_x || !acc_y || !acc_z) {
        return false;
    }
    
    *acc_x = JY901S_GetAccX(&handle->jy901s);
    *acc_y = JY901S_GetAccY(&handle->jy901s);
    *acc_z = JY901S_GetAccZ(&handle->jy901s);
    
    return true;
}

/**
 * @brief 获取角速度数据 (STM32封装)
 */
bool JY901S_STM32_GetGyroscope(JY901S_STM32_Handle_t *handle, 
                               float *gyro_x, float *gyro_y, float *gyro_z)
{
    if (!handle || !gyro_x || !gyro_y || !gyro_z) {
        return false;
    }
    
    *gyro_x = JY901S_GetGyroX(&handle->jy901s);
    *gyro_y = JY901S_GetGyroY(&handle->jy901s);
    *gyro_z = JY901S_GetGyroZ(&handle->jy901s);
    
    return true;
}

/**
 * @brief 获取温度数据 (STM32封装)
 */
float JY901S_STM32_GetTemperature(JY901S_STM32_Handle_t *handle)
{
    if (!handle) {
        return 0.0f;
    }
    
    return JY901S_GetTemperature(&handle->jy901s);
}

/**
 * @brief 校准传感器 (STM32封装)
 */
bool JY901S_STM32_Calibrate(JY901S_STM32_Handle_t *handle)
{
    if (!handle) {
        return false;
    }
    
    return JY901S_Calibrate(&handle->jy901s);
}

/**
 * @brief 复位传感器 (STM32封装)
 */
bool JY901S_STM32_Reset(JY901S_STM32_Handle_t *handle)
{
    if (!handle) {
        return false;
    }
    
    return JY901S_Reset(&handle->jy901s);
}

/**
 * @brief 检查数据是否就绪 (STM32封装)
 */
bool JY901S_STM32_IsDataReady(JY901S_STM32_Handle_t *handle)
{
    if (!handle) {
        return false;
    }
    
    return JY901S_IsDataReady(&handle->jy901s);
}

/**
 * @brief 获取错误计数 (STM32封装)
 */
uint32_t JY901S_STM32_GetErrorCount(JY901S_STM32_Handle_t *handle)
{
    if (!handle) {
        return 0;
    }
    
    return JY901S_GetErrorCount(&handle->jy901s);
}
