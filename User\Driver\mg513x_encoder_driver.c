#include "mg513x_encoder_driver.h"

// 全局变量用于运行时修改车轮直径
static float g_wheel_diameter_cm = MG513X_WHEEL_DIAMETER_CM;
static float g_wheel_circumference_cm = MG513X_WHEEL_CIRCUMFERENCE_CM;

/**
 * @brief 初始化MG513X编码器驱动
 */
void MG513X_Encoder_Init(MG513X_Encoder* encoder, TIM_HandleTypeDef *htim, uint8_t reverse)
{
    encoder->htim = htim;
    encoder->reverse = reverse;
    
    // 启动定时器的编码器模式
    HAL_TIM_Encoder_Start(encoder->htim, TIM_CHANNEL_ALL);

    // 清零计数器
    __HAL_TIM_SetCounter(encoder->htim, 0);

    // 初始化数据结构
    encoder->count = 0;
    encoder->total_count = 0;
    encoder->speed_cm_s = 0.0f;
    encoder->speed_rpm = 0.0f;
    encoder->distance_cm = 0.0f;
}

/**
 * @brief 更新MG513X编码器数据
 */
void MG513X_Encoder_Update(MG513X_Encoder* encoder)
{
    // 1. 读取原始计数值
    encoder->count = (int16_t)__HAL_TIM_GetCounter(encoder->htim);
    
    // 2. 处理编码器反向
    encoder->count = encoder->reverse == 0 ? encoder->count : -encoder->count;

    // 3. 清零硬件计数器，为下个周期做准备
    __HAL_TIM_SetCounter(encoder->htim, 0);

    // 4. 累计总数
    encoder->total_count += encoder->count;

    // 5. 计算线速度 (cm/s)
    // 速度 = (计数值 / PPR) * 周长 / 采样时间
    encoder->speed_cm_s = (float)encoder->count / MG513X_ENCODER_PPR * 
                          g_wheel_circumference_cm / MG513X_SAMPLING_TIME_S;
    
    // 6. 计算转速 (RPM)
    // 转速 = (计数值 / PPR) * (60秒/分钟) / 采样时间
    encoder->speed_rpm = (float)encoder->count / MG513X_ENCODER_PPR * 
                         60.0f / MG513X_SAMPLING_TIME_S;
    
    // 7. 计算累计行驶距离 (cm)
    float distance_increment = (float)encoder->count / MG513X_ENCODER_PPR * g_wheel_circumference_cm;
    encoder->distance_cm += distance_increment;
}

/**
 * @brief 获取编码器线速度 (cm/s)
 */
float MG513X_Encoder_GetSpeedCmS(MG513X_Encoder* encoder)
{
    return encoder->speed_cm_s;
}

/**
 * @brief 获取编码器转速 (RPM)
 */
float MG513X_Encoder_GetSpeedRPM(MG513X_Encoder* encoder)
{
    return encoder->speed_rpm;
}

/**
 * @brief 获取编码器总计数
 */
int32_t MG513X_Encoder_GetTotalCount(MG513X_Encoder* encoder)
{
    return encoder->total_count;
}

/**
 * @brief 获取累计行驶距离 (cm)
 */
float MG513X_Encoder_GetDistanceCm(MG513X_Encoder* encoder)
{
    return encoder->distance_cm;
}

/**
 * @brief 重置编码器计数和距离
 */
void MG513X_Encoder_Reset(MG513X_Encoder* encoder)
{
    encoder->count = 0;
    encoder->total_count = 0;
    encoder->speed_cm_s = 0.0f;
    encoder->speed_rpm = 0.0f;
    encoder->distance_cm = 0.0f;
    __HAL_TIM_SetCounter(encoder->htim, 0);
}

/**
 * @brief 设置车轮直径 (运行时修改)
 */
void MG513X_Encoder_SetWheelDiameter(float diameter_cm)
{
    g_wheel_diameter_cm = diameter_cm;
    g_wheel_circumference_cm = diameter_cm * PI;
}

/**
 * @brief 获取当前车轮直径设置
 */
float MG513X_Encoder_GetWheelDiameter(void)
{
    return g_wheel_diameter_cm;
}
