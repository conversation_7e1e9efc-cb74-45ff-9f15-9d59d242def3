#include "mg513x_driver.h"

// 内部辅助函数声明
static void MG513X_SetDirection(MG513X_Motor* motor, uint8_t direction);
static int MG513X_ApplyDeadBandCompensation(MG513X_Motor* motor, int speed);
static int MG513X_LimitSpeed(int speed);

/**
 * @brief 初始化MG513X编码电机
 */
void MG513X_Init(MG513X_Motor* motor, 
                 TIM_HandleTypeDef *pwm_htim, uint32_t pwm_channel,
                 GPIO_TypeDef *dir1_port, uint16_t dir1_pin,
                 GPIO_TypeDef *dir2_port, uint16_t dir2_pin,
                 TIM_HandleTypeDef *encoder_htim,
                 uint8_t motor_reverse, uint8_t encoder_reverse,
                 int dead_band_speed)
{
    // 配置参数初始化
    motor->config.pwm_htim = pwm_htim;
    motor->config.pwm_channel = pwm_channel;
    motor->config.dir1.port = dir1_port;
    motor->config.dir1.pin = dir1_pin;
    motor->config.dir2.port = dir2_port;
    motor->config.dir2.pin = dir2_pin;
    motor->config.encoder_htim = encoder_htim;
    motor->config.motor_reverse = motor_reverse;
    motor->config.encoder_reverse = encoder_reverse;
    motor->config.dead_band_speed = dead_band_speed;
    
    // 数据初始化
    motor->target_speed = 0;
    motor->current_speed = 0;
    motor->encoder_count = 0;
    motor->encoder_total_count = 0;
    motor->speed_cm_s = 0.0f;
    motor->speed_rpm = 0.0f;
    motor->is_running = 0;
    motor->direction = 0;
    
    // 硬件初始化
    // 设置方向控制引脚为停止状态
    HAL_GPIO_WritePin(motor->config.dir1.port, motor->config.dir1.pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(motor->config.dir2.port, motor->config.dir2.pin, GPIO_PIN_RESET);
    
    // 启动PWM
    HAL_TIM_PWM_Start(motor->config.pwm_htim, motor->config.pwm_channel);
    __HAL_TIM_SET_COMPARE(motor->config.pwm_htim, motor->config.pwm_channel, 0);
    
    // 启动编码器
    HAL_TIM_Encoder_Start(motor->config.encoder_htim, TIM_CHANNEL_ALL);
    __HAL_TIM_SetCounter(motor->config.encoder_htim, 0);
}

/**
 * @brief 设置电机速度
 */
void MG513X_SetSpeed(MG513X_Motor* motor, int speed)
{
    // 限制速度范围
    speed = MG513X_LimitSpeed(speed);
    motor->target_speed = speed;
    
    // 应用死区补偿
    int compensated_speed = MG513X_ApplyDeadBandCompensation(motor, speed);
    motor->current_speed = compensated_speed;
    
    if (compensated_speed == 0) {
        // 停止
        MG513X_Stop(motor);
    } else {
        // 设置方向和PWM
        uint8_t direction = (compensated_speed > 0) ? 0 : 1;
        MG513X_SetDirection(motor, direction);
        
        // 设置PWM占空比
        uint32_t pwm_value = (compensated_speed > 0) ? compensated_speed : -compensated_speed;
        __HAL_TIM_SET_COMPARE(motor->config.pwm_htim, motor->config.pwm_channel, pwm_value);
        
        motor->is_running = 1;
        motor->direction = direction;
    }
}

/**
 * @brief 更新编码器数据
 */
void MG513X_UpdateEncoder(MG513X_Motor* motor)
{
    // 读取编码器计数
    motor->encoder_count = (int16_t)__HAL_TIM_GetCounter(motor->config.encoder_htim);
    
    // 处理编码器方向
    if (motor->config.encoder_reverse) {
        motor->encoder_count = -motor->encoder_count;
    }
    
    // 清零硬件计数器
    __HAL_TIM_SetCounter(motor->config.encoder_htim, 0);
    
    // 累计总计数
    motor->encoder_total_count += motor->encoder_count;
    
    // 计算线速度 (cm/s)
    motor->speed_cm_s = (float)motor->encoder_count / MG513X_ENCODER_PPR * 
                        MG513X_WHEEL_CIRCUMFERENCE_CM / MG513X_SAMPLING_TIME_S;
    
    // 计算转速 (RPM)
    motor->speed_rpm = (float)motor->encoder_count / MG513X_ENCODER_PPR * 
                       60.0f / MG513X_SAMPLING_TIME_S;
}

/**
 * @brief 电机停止 (自由停止)
 */
void MG513X_Stop(MG513X_Motor* motor)
{
    HAL_GPIO_WritePin(motor->config.dir1.port, motor->config.dir1.pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(motor->config.dir2.port, motor->config.dir2.pin, GPIO_PIN_RESET);
    __HAL_TIM_SET_COMPARE(motor->config.pwm_htim, motor->config.pwm_channel, 0);
    
    motor->current_speed = 0;
    motor->is_running = 0;
}

/**
 * @brief 电机刹车 (短路刹车)
 */
void MG513X_Brake(MG513X_Motor* motor)
{
    HAL_GPIO_WritePin(motor->config.dir1.port, motor->config.dir1.pin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(motor->config.dir2.port, motor->config.dir2.pin, GPIO_PIN_SET);
    __HAL_TIM_SET_COMPARE(motor->config.pwm_htim, motor->config.pwm_channel, 0);
    
    motor->current_speed = 0;
    motor->is_running = 0;
}

/**
 * @brief 获取电机实际速度 (cm/s)
 */
float MG513X_GetSpeedCmS(MG513X_Motor* motor)
{
    return motor->speed_cm_s;
}

/**
 * @brief 获取电机实际转速 (RPM)
 */
float MG513X_GetSpeedRPM(MG513X_Motor* motor)
{
    return motor->speed_rpm;
}

/**
 * @brief 获取编码器总计数
 */
int32_t MG513X_GetTotalCount(MG513X_Motor* motor)
{
    return motor->encoder_total_count;
}

/**
 * @brief 重置编码器计数
 */
void MG513X_ResetEncoder(MG513X_Motor* motor)
{
    motor->encoder_count = 0;
    motor->encoder_total_count = 0;
    motor->speed_cm_s = 0.0f;
    motor->speed_rpm = 0.0f;
    __HAL_TIM_SetCounter(motor->config.encoder_htim, 0);
}

// --- 内部辅助函数实现 ---

/**
 * @brief 设置电机方向
 */
static void MG513X_SetDirection(MG513X_Motor* motor, uint8_t direction)
{
    // 根据电机反转配置调整方向
    if (motor->config.motor_reverse) {
        direction = !direction;
    }
    
    if (direction == 0) {
        // 正转
        HAL_GPIO_WritePin(motor->config.dir1.port, motor->config.dir1.pin, GPIO_PIN_SET);
        HAL_GPIO_WritePin(motor->config.dir2.port, motor->config.dir2.pin, GPIO_PIN_RESET);
    } else {
        // 反转
        HAL_GPIO_WritePin(motor->config.dir1.port, motor->config.dir1.pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(motor->config.dir2.port, motor->config.dir2.pin, GPIO_PIN_SET);
    }
}

/**
 * @brief 应用死区补偿
 */
static int MG513X_ApplyDeadBandCompensation(MG513X_Motor* motor, int speed)
{
    if (speed > 0 && speed < motor->config.dead_band_speed) {
        return motor->config.dead_band_speed;
    } else if (speed < 0 && speed > -motor->config.dead_band_speed) {
        return -motor->config.dead_band_speed;
    }
    return speed;
}

/**
 * @brief 限制速度范围
 */
static int MG513X_LimitSpeed(int speed)
{
    if (speed > MG513X_MAX_SPEED) {
        return MG513X_MAX_SPEED;
    } else if (speed < MG513X_MIN_SPEED) {
        return MG513X_MIN_SPEED;
    }
    return speed;
}
