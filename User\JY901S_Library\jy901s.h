/**
 * @file jy901s.h
 * @brief JY901S陀螺仪I2C驱动库 - 便于移植版本
 * @version 1.0
 * @date 2024-07-28
 * <AUTHOR> Agent
 * 
 * @note 这是一个高度模块化、便于移植的JY901S驱动库
 *       只需要实现4个移植接口函数即可在任何平台使用
 */

#ifndef __JY901S_H__
#define __JY901S_H__

#include <stdint.h>
#include <stdbool.h>

// --- JY901S设备参数 ---
#define JY901S_I2C_ADDR         0x50    // JY901S默认I2C地址 (7位地址)
#define JY901S_TIMEOUT_MS       1000    // 默认超时时间

// --- JY901S寄存器地址 ---
#define JY901S_REG_TIME         0x30    // 时间寄存器
#define JY901S_REG_ACC          0x34    // 加速度寄存器
#define JY901S_REG_GYRO         0x37    // 角速度寄存器  
#define JY901S_REG_ANGLE        0x3D    // 角度寄存器
#define JY901S_REG_MAG          0x3A    // 磁场寄存器
#define JY901S_REG_TEMP         0x46    // 温度寄存器

// --- 控制寄存器 ---
#define JY901S_REG_SAVE         0x00    // 保存设置
#define JY901S_REG_CALSW        0x01    // 校准开关
#define JY901S_REG_RSW          0x02    // 回传速率设置
#define JY901S_REG_RRATE        0x03    // 回传内容设置

// --- 数据转换系数 ---
#define JY901S_ACC_SCALE        (16.0f / 32768.0f)      // 加速度量程 ±16g
#define JY901S_GYRO_SCALE       (2000.0f / 32768.0f)    // 角速度量程 ±2000°/s
#define JY901S_ANGLE_SCALE      (180.0f / 32768.0f)     // 角度量程 ±180°
#define JY901S_TEMP_SCALE       (100.0f / 32768.0f)     // 温度量程

/**
 * @brief JY901S数据结构体
 */
typedef struct {
    // 原始数据 (16位有符号整数)
    int16_t acc_raw[3];         // 加速度原始数据 [X, Y, Z]
    int16_t gyro_raw[3];        // 角速度原始数据 [X, Y, Z]
    int16_t angle_raw[3];       // 角度原始数据 [Roll, Pitch, Yaw]
    int16_t mag_raw[3];         // 磁场原始数据 [X, Y, Z]
    int16_t temp_raw;           // 温度原始数据
    
    // 转换后的物理量
    float acc[3];               // 加速度 (g) [X, Y, Z]
    float gyro[3];              // 角速度 (°/s) [X, Y, Z]
    float angle[3];             // 角度 (°) [Roll, Pitch, Yaw]
    float mag[3];               // 磁场强度 [X, Y, Z]
    float temperature;          // 温度 (°C)
    
    // 状态信息
    bool data_ready;            // 数据就绪标志
    uint32_t timestamp;         // 数据时间戳
    uint32_t error_count;       // 错误计数
} JY901S_Data_t;

/**
 * @brief JY901S配置结构体
 */
typedef struct {
    uint8_t device_addr;        // I2C设备地址
    uint16_t timeout_ms;        // 通信超时时间
    bool auto_calibration;      // 是否自动校准
    uint8_t output_rate;        // 数据输出频率
} JY901S_Config_t;

// --- 移植接口函数类型定义 ---

/**
 * @brief I2C写函数类型
 * @param device_addr I2C设备地址 (7位地址)
 * @param reg_addr 寄存器地址
 * @param data 要写入的数据指针
 * @param len 数据长度
 * @param timeout_ms 超时时间 (毫秒)
 * @return true-成功, false-失败
 */
typedef bool (*JY901S_I2C_Write_t)(uint8_t device_addr, uint8_t reg_addr, 
                                   uint8_t *data, uint16_t len, uint16_t timeout_ms);

/**
 * @brief I2C读函数类型
 * @param device_addr I2C设备地址 (7位地址)
 * @param reg_addr 寄存器地址
 * @param data 读取数据缓冲区指针
 * @param len 数据长度
 * @param timeout_ms 超时时间 (毫秒)
 * @return true-成功, false-失败
 */
typedef bool (*JY901S_I2C_Read_t)(uint8_t device_addr, uint8_t reg_addr, 
                                  uint8_t *data, uint16_t len, uint16_t timeout_ms);

/**
 * @brief 延时函数类型
 * @param ms 延时毫秒数
 */
typedef void (*JY901S_Delay_t)(uint32_t ms);

/**
 * @brief 获取系统时间戳函数类型
 * @return 系统时间戳 (毫秒)
 */
typedef uint32_t (*JY901S_GetTick_t)(void);

/**
 * @brief JY901S驱动句柄
 */
typedef struct {
    JY901S_Config_t config;     // 配置参数
    JY901S_Data_t data;         // 传感器数据
    
    // 移植接口函数指针
    JY901S_I2C_Write_t i2c_write;
    JY901S_I2C_Read_t i2c_read;
    JY901S_Delay_t delay;
    JY901S_GetTick_t get_tick;
    
    // 内部状态
    bool initialized;           // 初始化状态
    uint32_t last_read_time;    // 上次读取时间
} JY901S_Handle_t;

// --- 公共API函数声明 ---

/**
 * @brief 初始化JY901S驱动
 * @param handle JY901S句柄指针
 * @param config 配置参数指针
 * @param i2c_write I2C写函数指针
 * @param i2c_read I2C读函数指针
 * @param delay 延时函数指针
 * @param get_tick 获取时间戳函数指针
 * @return true-成功, false-失败
 */
bool JY901S_Init(JY901S_Handle_t *handle, 
                 JY901S_Config_t *config,
                 JY901S_I2C_Write_t i2c_write,
                 JY901S_I2C_Read_t i2c_read,
                 JY901S_Delay_t delay,
                 JY901S_GetTick_t get_tick);

/**
 * @brief 读取所有传感器数据
 * @param handle JY901S句柄指针
 * @return true-成功, false-失败
 */
bool JY901S_ReadAllData(JY901S_Handle_t *handle);

/**
 * @brief 读取加速度数据
 * @param handle JY901S句柄指针
 * @return true-成功, false-失败
 */
bool JY901S_ReadAcceleration(JY901S_Handle_t *handle);

/**
 * @brief 读取角速度数据
 * @param handle JY901S句柄指针
 * @return true-成功, false-失败
 */
bool JY901S_ReadGyroscope(JY901S_Handle_t *handle);

/**
 * @brief 读取角度数据
 * @param handle JY901S句柄指针
 * @return true-成功, false-失败
 */
bool JY901S_ReadAngle(JY901S_Handle_t *handle);

/**
 * @brief 读取温度数据
 * @param handle JY901S句柄指针
 * @return true-成功, false-失败
 */
bool JY901S_ReadTemperature(JY901S_Handle_t *handle);

/**
 * @brief 校准传感器
 * @param handle JY901S句柄指针
 * @return true-成功, false-失败
 */
bool JY901S_Calibrate(JY901S_Handle_t *handle);

/**
 * @brief 复位传感器
 * @param handle JY901S句柄指针
 * @return true-成功, false-失败
 */
bool JY901S_Reset(JY901S_Handle_t *handle);

// --- 数据获取函数 ---

/**
 * @brief 获取Roll角度
 * @param handle JY901S句柄指针
 * @return Roll角度 (°)
 */
float JY901S_GetRoll(JY901S_Handle_t *handle);

/**
 * @brief 获取Pitch角度
 * @param handle JY901S句柄指针
 * @return Pitch角度 (°)
 */
float JY901S_GetPitch(JY901S_Handle_t *handle);

/**
 * @brief 获取Yaw角度
 * @param handle JY901S句柄指针
 * @return Yaw角度 (°)
 */
float JY901S_GetYaw(JY901S_Handle_t *handle);

/**
 * @brief 获取X轴加速度
 * @param handle JY901S句柄指针
 * @return X轴加速度 (g)
 */
float JY901S_GetAccX(JY901S_Handle_t *handle);

/**
 * @brief 获取Y轴加速度
 * @param handle JY901S句柄指针
 * @return Y轴加速度 (g)
 */
float JY901S_GetAccY(JY901S_Handle_t *handle);

/**
 * @brief 获取Z轴加速度
 * @param handle JY901S句柄指针
 * @return Z轴加速度 (g)
 */
float JY901S_GetAccZ(JY901S_Handle_t *handle);

/**
 * @brief 获取X轴角速度
 * @param handle JY901S句柄指针
 * @return X轴角速度 (°/s)
 */
float JY901S_GetGyroX(JY901S_Handle_t *handle);

/**
 * @brief 获取Y轴角速度
 * @param handle JY901S句柄指针
 * @return Y轴角速度 (°/s)
 */
float JY901S_GetGyroY(JY901S_Handle_t *handle);

/**
 * @brief 获取Z轴角速度
 * @param handle JY901S句柄指针
 * @return Z轴角速度 (°/s)
 */
float JY901S_GetGyroZ(JY901S_Handle_t *handle);

/**
 * @brief 获取温度
 * @param handle JY901S句柄指针
 * @return 温度 (°C)
 */
float JY901S_GetTemperature(JY901S_Handle_t *handle);

/**
 * @brief 检查数据是否就绪
 * @param handle JY901S句柄指针
 * @return true-数据就绪, false-数据未就绪
 */
bool JY901S_IsDataReady(JY901S_Handle_t *handle);

/**
 * @brief 获取错误计数
 * @param handle JY901S句柄指针
 * @return 错误计数
 */
uint32_t JY901S_GetErrorCount(JY901S_Handle_t *handle);

#endif /* __JY901S_H__ */
