/**
 * @file jy901s_example.c
 * @brief JY901S陀螺仪使用示例
 * @version 1.0
 * @date 2024-07-28
 * <AUTHOR> Agent
 * 
 * @note 这个文件展示了如何使用JY901S库
 */

#include "jy901s_stm32.h"
#include <stdio.h>

// JY901S句柄
static JY901S_STM32_Handle_t jy901s_handle;

/**
 * @brief JY901S初始化示例
 * @note 在您的初始化函数中调用此函数
 */
void JY901S_Example_Init(void)
{
    // 配置JY901S参数
    JY901S_STM32_Config_t config = JY901S_STM32_STANDARD_CONFIG(&hi2c1);
    
    // 或者使用自定义配置
    // JY901S_STM32_Config_t config = {
    //     .hi2c = &hi2c1,                    // I2C句柄
    //     .device_addr = JY901S_I2C_ADDR,    // 设备地址 0x50
    //     .timeout_ms = 1000,                // 超时时间 1000ms
    //     .auto_calibration = false          // 不自动校准
    // };
    
    // 初始化JY901S
    if (JY901S_STM32_Init(&jy901s_handle, &config)) {
        printf("JY901S initialized successfully!\r\n");
        
        // 可选：手动校准
        // printf("Calibrating JY901S...\r\n");
        // JY901S_STM32_Calibrate(&jy901s_handle);
        // printf("Calibration completed!\r\n");
    } else {
        printf("JY901S initialization failed!\r\n");
        printf("Please check:\r\n");
        printf("1. I2C wiring (SDA, SCL)\r\n");
        printf("2. Power supply (3.3V or 5V)\r\n");
        printf("3. I2C configuration\r\n");
    }
}

/**
 * @brief JY901S数据读取示例
 * @note 在您的主循环或定时器中调用此函数
 */
void JY901S_Example_ReadData(void)
{
    // 读取所有传感器数据
    if (JY901S_STM32_ReadAllData(&jy901s_handle)) {
        // 获取角度数据
        float roll = JY901S_STM32_GetRoll(&jy901s_handle);
        float pitch = JY901S_STM32_GetPitch(&jy901s_handle);
        float yaw = JY901S_STM32_GetYaw(&jy901s_handle);
        
        // 获取加速度数据
        float acc_x, acc_y, acc_z;
        JY901S_STM32_GetAcceleration(&jy901s_handle, &acc_x, &acc_y, &acc_z);
        
        // 获取角速度数据
        float gyro_x, gyro_y, gyro_z;
        JY901S_STM32_GetGyroscope(&jy901s_handle, &gyro_x, &gyro_y, &gyro_z);
        
        // 获取温度数据
        float temperature = JY901S_STM32_GetTemperature(&jy901s_handle);
        
        // 打印数据 (可选)
        printf("Angle: R=%.1f° P=%.1f° Y=%.1f°\r\n", roll, pitch, yaw);
        printf("Acc: X=%.2fg Y=%.2fg Z=%.2fg\r\n", acc_x, acc_y, acc_z);
        printf("Gyro: X=%.1f°/s Y=%.1f°/s Z=%.1f°/s\r\n", gyro_x, gyro_y, gyro_z);
        printf("Temp: %.1f°C\r\n", temperature);
        printf("---\r\n");
    } else {
        printf("JY901S read failed! Error count: %lu\r\n", 
               JY901S_STM32_GetErrorCount(&jy901s_handle));
    }
}

/**
 * @brief JY901S在PID控制中的应用示例
 */
void JY901S_Example_PID_Control(void)
{
    // 读取传感器数据
    if (JY901S_STM32_ReadAllData(&jy901s_handle)) {
        // 获取当前偏航角用于角度环控制
        float current_yaw = JY901S_STM32_GetYaw(&jy901s_handle);
        
        // 获取Z轴角速度用于姿态稳定
        float gyro_z, gyro_x, gyro_y;
        JY901S_STM32_GetGyroscope(&jy901s_handle, &gyro_x, &gyro_y, &gyro_z);
        
        // 在这里添加您的PID控制算法
        // 例如：
        // int angle_pid_output = pid_calculate(&pid_angle, current_yaw);
        // int gyro_compensation = (int)(gyro_z * 0.5f);
        // 
        // int left_motor_speed = base_speed + angle_pid_output - gyro_compensation;
        // int right_motor_speed = base_speed - angle_pid_output + gyro_compensation;
        // 
        // Motor_SetSpeed(left_motor_speed, right_motor_speed);
        
        printf("PID Control: Yaw=%.1f° GyroZ=%.1f°/s\r\n", current_yaw, gyro_z);
    }
}

/**
 * @brief JY901S状态监控示例
 */
void JY901S_Example_StatusMonitor(void)
{
    // 检查数据是否就绪
    if (JY901S_STM32_IsDataReady(&jy901s_handle)) {
        printf("JY901S: Data ready\r\n");
    } else {
        printf("JY901S: Data not ready\r\n");
    }
    
    // 检查错误计数
    uint32_t error_count = JY901S_STM32_GetErrorCount(&jy901s_handle);
    if (error_count > 0) {
        printf("JY901S: Error count = %lu\r\n", error_count);
    }
    
    // 获取传感器温度用于监控
    float temperature = JY901S_STM32_GetTemperature(&jy901s_handle);
    if (temperature < -10.0f || temperature > 60.0f) {
        printf("JY901S: Temperature warning = %.1f°C\r\n", temperature);
    }
}

/**
 * @brief JY901S校准示例
 */
void JY901S_Example_Calibration(void)
{
    printf("Starting JY901S calibration...\r\n");
    printf("Please keep the sensor stationary!\r\n");
    
    if (JY901S_STM32_Calibrate(&jy901s_handle)) {
        printf("JY901S calibration completed successfully!\r\n");
    } else {
        printf("JY901S calibration failed!\r\n");
    }
}

/**
 * @brief JY901S复位示例
 */
void JY901S_Example_Reset(void)
{
    printf("Resetting JY901S...\r\n");
    
    if (JY901S_STM32_Reset(&jy901s_handle)) {
        printf("JY901S reset completed successfully!\r\n");
    } else {
        printf("JY901S reset failed!\r\n");
    }
}

/**
 * @brief 完整的JY901S测试程序
 */
void JY901S_Example_FullTest(void)
{
    static uint32_t test_step = 0;
    static uint32_t step_timer = 0;
    
    step_timer++;
    
    switch (test_step) {
        case 0: // 初始化测试
            if (step_timer == 1) {
                printf("=== JY901S Full Test Start ===\r\n");
                JY901S_Example_Init();
            }
            if (step_timer >= 10) { // 1秒后进入下一步
                test_step = 1;
                step_timer = 0;
            }
            break;
            
        case 1: // 数据读取测试 (10秒)
            if (step_timer % 10 == 0) { // 每1秒读取一次
                JY901S_Example_ReadData();
            }
            if (step_timer >= 100) { // 10秒后进入下一步
                test_step = 2;
                step_timer = 0;
            }
            break;
            
        case 2: // 校准测试
            if (step_timer == 1) {
                JY901S_Example_Calibration();
            }
            if (step_timer >= 50) { // 5秒后进入下一步
                test_step = 3;
                step_timer = 0;
            }
            break;
            
        case 3: // 复位测试
            if (step_timer == 1) {
                JY901S_Example_Reset();
            }
            if (step_timer >= 20) { // 2秒后进入下一步
                test_step = 4;
                step_timer = 0;
            }
            break;
            
        case 4: // 持续监控
            if (step_timer % 50 == 0) { // 每5秒监控一次
                JY901S_Example_StatusMonitor();
                JY901S_Example_ReadData();
            }
            break;
            
        default:
            test_step = 0;
            step_timer = 0;
            break;
    }
}

/**
 * @brief 简单的使用示例 (推荐)
 */
void JY901S_Example_Simple(void)
{
    static bool initialized = false;
    static uint32_t read_timer = 0;
    
    // 初始化 (只执行一次)
    if (!initialized) {
        JY901S_STM32_Config_t config = JY901S_STM32_STANDARD_CONFIG(&hi2c1);
        
        if (JY901S_STM32_Init(&jy901s_handle, &config)) {
            printf("JY901S ready!\r\n");
            initialized = true;
        } else {
            printf("JY901S init failed!\r\n");
            return;
        }
    }
    
    // 周期性读取数据 (每100ms读取一次)
    read_timer++;
    if (read_timer >= 10) { // 假设此函数每10ms调用一次
        read_timer = 0;
        
        if (JY901S_STM32_ReadAllData(&jy901s_handle)) {
            // 获取需要的数据
            float yaw = JY901S_STM32_GetYaw(&jy901s_handle);
            float roll = JY901S_STM32_GetRoll(&jy901s_handle);
            float pitch = JY901S_STM32_GetPitch(&jy901s_handle);
            
            // 在这里使用数据进行控制
            // 例如：PID控制、状态显示等
            
            // 可选：打印数据
            printf("Y=%.1f° R=%.1f° P=%.1f°\r\n", yaw, roll, pitch);
        }
    }
}
