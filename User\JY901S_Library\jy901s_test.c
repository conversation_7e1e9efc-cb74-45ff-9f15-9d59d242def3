/**
 * @file jy901s_test.c
 * @brief JY901S陀螺仪测试程序
 * @version 1.0
 * @date 2024-07-28
 * <AUTHOR> Agent
 * 
 * @note 这个文件提供了JY901S库的测试程序
 *       可以用来验证库的功能是否正常
 */

#include "jy901s_stm32.h"
#include <stdio.h>

// 测试状态枚举
typedef enum {
    TEST_STATE_INIT = 0,
    TEST_STATE_READ_DATA,
    TEST_STATE_CALIBRATE,
    TEST_STATE_RESET,
    TEST_STATE_PERFORMANCE,
    TEST_STATE_COMPLETE
} JY901S_Test_State_t;

// 测试句柄和状态
static JY901S_STM32_Handle_t test_handle;
static JY901S_Test_State_t test_state = TEST_STATE_INIT;
static uint32_t test_timer = 0;
static uint32_t test_success_count = 0;
static uint32_t test_fail_count = 0;

/**
 * @brief JY901S测试主函数
 * @note 在main函数的while循环中周期性调用此函数 (建议10ms一次)
 */
void JY901S_Test_Main(void)
{
    test_timer++;
    
    switch (test_state) {
        case TEST_STATE_INIT:
            JY901S_Test_Init();
            break;
            
        case TEST_STATE_READ_DATA:
            JY901S_Test_ReadData();
            break;
            
        case TEST_STATE_CALIBRATE:
            JY901S_Test_Calibrate();
            break;
            
        case TEST_STATE_RESET:
            JY901S_Test_Reset();
            break;
            
        case TEST_STATE_PERFORMANCE:
            JY901S_Test_Performance();
            break;
            
        case TEST_STATE_COMPLETE:
            JY901S_Test_Complete();
            break;
            
        default:
            test_state = TEST_STATE_INIT;
            test_timer = 0;
            break;
    }
}

/**
 * @brief 初始化测试
 */
void JY901S_Test_Init(void)
{
    if (test_timer == 1) {
        printf("\r\n=== JY901S Driver Test Start ===\r\n");
        printf("Test Step 1: Initialization\r\n");
        
        // 配置JY901S
        JY901S_STM32_Config_t config = JY901S_STM32_STANDARD_CONFIG(&hi2c1);
        
        // 初始化JY901S
        if (JY901S_STM32_Init(&test_handle, &config)) {
            printf("✓ JY901S initialization successful\r\n");
            printf("  Device Address: 0x%02X\r\n", config.device_addr);
            printf("  Timeout: %dms\r\n", config.timeout_ms);
            test_success_count++;
        } else {
            printf("✗ JY901S initialization failed\r\n");
            printf("  Please check:\r\n");
            printf("  - I2C wiring (SDA, SCL)\r\n");
            printf("  - Power supply (3.3V or 5V)\r\n");
            printf("  - I2C configuration\r\n");
            test_fail_count++;
        }
    }
    
    // 等待1秒后进入下一个测试
    if (test_timer >= 100) {
        test_state = TEST_STATE_READ_DATA;
        test_timer = 0;
    }
}

/**
 * @brief 数据读取测试
 */
void JY901S_Test_ReadData(void)
{
    if (test_timer == 1) {
        printf("\r\nTest Step 2: Data Reading (10 seconds)\r\n");
    }
    
    // 每1秒读取一次数据
    if (test_timer % 100 == 0) {
        if (JY901S_STM32_ReadAllData(&test_handle)) {
            float roll = JY901S_STM32_GetRoll(&test_handle);
            float pitch = JY901S_STM32_GetPitch(&test_handle);
            float yaw = JY901S_STM32_GetYaw(&test_handle);
            float temp = JY901S_STM32_GetTemperature(&test_handle);
            
            printf("  %.1fs: R=%.1f° P=%.1f° Y=%.1f° T=%.1f°C\r\n", 
                   test_timer / 100.0f, roll, pitch, yaw, temp);
            
            // 检查数据合理性
            if (JY901S_Test_ValidateData(roll, pitch, yaw, temp)) {
                test_success_count++;
            } else {
                test_fail_count++;
                printf("  ⚠ Warning: Data out of expected range\r\n");
            }
        } else {
            printf("  ✗ Data read failed at %.1fs\r\n", test_timer / 100.0f);
            test_fail_count++;
        }
    }
    
    // 10秒后进入下一个测试
    if (test_timer >= 1000) {
        test_state = TEST_STATE_CALIBRATE;
        test_timer = 0;
    }
}

/**
 * @brief 校准测试
 */
void JY901S_Test_Calibrate(void)
{
    if (test_timer == 1) {
        printf("\r\nTest Step 3: Calibration\r\n");
        printf("  Please keep the sensor stationary...\r\n");
        
        if (JY901S_STM32_Calibrate(&test_handle)) {
            printf("✓ Calibration command sent successfully\r\n");
            test_success_count++;
        } else {
            printf("✗ Calibration command failed\r\n");
            test_fail_count++;
        }
    }
    
    // 等待5秒后进入下一个测试 (校准需要约3秒)
    if (test_timer >= 500) {
        test_state = TEST_STATE_RESET;
        test_timer = 0;
    }
}

/**
 * @brief 复位测试
 */
void JY901S_Test_Reset(void)
{
    if (test_timer == 1) {
        printf("\r\nTest Step 4: Reset\r\n");
        
        if (JY901S_STM32_Reset(&test_handle)) {
            printf("✓ Reset command sent successfully\r\n");
            test_success_count++;
        } else {
            printf("✗ Reset command failed\r\n");
            test_fail_count++;
        }
    }
    
    // 等待2秒后进入下一个测试
    if (test_timer >= 200) {
        test_state = TEST_STATE_PERFORMANCE;
        test_timer = 0;
    }
}

/**
 * @brief 性能测试
 */
void JY901S_Test_Performance(void)
{
    if (test_timer == 1) {
        printf("\r\nTest Step 5: Performance Test\r\n");
    }
    
    // 连续读取100次数据测试性能
    if (test_timer <= 100) {
        uint32_t start_tick = HAL_GetTick();
        bool result = JY901S_STM32_ReadAllData(&test_handle);
        uint32_t end_tick = HAL_GetTick();
        
        uint32_t read_time_ms = end_tick - start_tick;
        
        if (result) {
            test_success_count++;
            if (test_timer == 50) { // 中间打印一次性能数据
                printf("  Read time: %lums per operation\r\n", read_time_ms);
                if (read_time_ms < 10) {
                    printf("  ✓ Performance: Excellent (< 10ms)\r\n");
                } else if (read_time_ms < 50) {
                    printf("  ✓ Performance: Good (< 50ms)\r\n");
                } else {
                    printf("  ⚠ Performance: Slow (> 50ms)\r\n");
                }
            }
        } else {
            test_fail_count++;
        }
    }
    
    // 性能测试完成后进入最终状态
    if (test_timer >= 100) {
        test_state = TEST_STATE_COMPLETE;
        test_timer = 0;
    }
}

/**
 * @brief 测试完成
 */
void JY901S_Test_Complete(void)
{
    if (test_timer == 1) {
        printf("\r\n=== JY901S Driver Test Complete ===\r\n");
        printf("Test Results:\r\n");
        printf("  Success: %lu\r\n", test_success_count);
        printf("  Failed:  %lu\r\n", test_fail_count);
        printf("  Total:   %lu\r\n", test_success_count + test_fail_count);
        
        float success_rate = (float)test_success_count / (test_success_count + test_fail_count) * 100.0f;
        printf("  Success Rate: %.1f%%\r\n", success_rate);
        
        if (success_rate > 90.0f) {
            printf("✓ Test Result: EXCELLENT - JY901S driver is ready for use!\r\n");
        } else if (success_rate > 70.0f) {
            printf("✓ Test Result: GOOD - JY901S driver works with minor issues\r\n");
        } else {
            printf("✗ Test Result: POOR - Please check hardware and configuration\r\n");
        }
        
        printf("Error Count: %lu\r\n", JY901S_STM32_GetErrorCount(&test_handle));
        printf("===========================================\r\n");
    }
    
    // 每10秒打印一次实时数据
    if (test_timer % 1000 == 500) {
        printf("\r\n--- Real-time Data ---\r\n");
        if (JY901S_STM32_ReadAllData(&test_handle)) {
            float roll = JY901S_STM32_GetRoll(&test_handle);
            float pitch = JY901S_STM32_GetPitch(&test_handle);
            float yaw = JY901S_STM32_GetYaw(&test_handle);
            float acc_x, acc_y, acc_z;
            float gyro_x, gyro_y, gyro_z;
            
            JY901S_STM32_GetAcceleration(&test_handle, &acc_x, &acc_y, &acc_z);
            JY901S_STM32_GetGyroscope(&test_handle, &gyro_x, &gyro_y, &gyro_z);
            
            printf("Angle: R=%.1f° P=%.1f° Y=%.1f°\r\n", roll, pitch, yaw);
            printf("Acc: X=%.2fg Y=%.2fg Z=%.2fg\r\n", acc_x, acc_y, acc_z);
            printf("Gyro: X=%.1f°/s Y=%.1f°/s Z=%.1f°/s\r\n", gyro_x, gyro_y, gyro_z);
            printf("Temp: %.1f°C\r\n", JY901S_STM32_GetTemperature(&test_handle));
        }
        printf("----------------------\r\n");
    }
}

/**
 * @brief 验证数据有效性
 */
bool JY901S_Test_ValidateData(float roll, float pitch, float yaw, float temp)
{
    bool valid = true;
    
    // 检查角度范围
    if (roll < -180.0f || roll > 180.0f) valid = false;
    if (pitch < -180.0f || pitch > 180.0f) valid = false;
    if (yaw < -180.0f || yaw > 180.0f) valid = false;
    
    // 检查温度范围
    if (temp < -50.0f || temp > 100.0f) valid = false;
    
    return valid;
}

/**
 * @brief 获取测试状态
 */
JY901S_Test_State_t JY901S_Test_GetState(void)
{
    return test_state;
}

/**
 * @brief 获取测试成功率
 */
float JY901S_Test_GetSuccessRate(void)
{
    uint32_t total = test_success_count + test_fail_count;
    if (total == 0) return 0.0f;
    
    return (float)test_success_count / total * 100.0f;
}
